<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianDafOpeModel extends MY_Model
{
    protected $_table = 'medis.tb_pendaftaran_operasi';
    protected $_primary_key = 'id';
    protected $_order_by = 'created_at';
    protected $_order_by_type = 'desc';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Ambil referensi kelas operasi (JENIS=19) ASC
     * @return array
     */
    public function getKelasOperasi()
    {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('master.referensi');
        $this->db->where('JENIS', 19);
        $this->db->where('STATUS', 1);
        $this->db->order_by('DESKRIPSI', 'ASC');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function rules()
    {
        return [
            [
                'field' => 'nokun',
                'label' => '<PERSON><PERSON> kunjungan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'diagnosa_medis',
                'label' => 'Diagnosis medis',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'ruang_tujuan',
                'label' => 'Ruang tujuan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'perkiraan_lama_operasi',
                'label' => 'Perkiraan lama operasi',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'tujuan_operasi',
                'label' => 'Tujuan operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'sifat_operasi',
                'label' => 'Sifat operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'rencana_jenis_pembiusan',
                'label' => 'Rencana jenis pembiusan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'potong_beku',
                'label' => 'Potong beku',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'ruang_operasi',
                'label' => 'Ruangan Operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesCITO()
    {
        return [
            [
                'field' => 'sifat_operasi_lain',
                'label' => 'Alasan CITO',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesUrgent()
    {
        return [
            [
                'field' => 'alasan_urgent',
                'label' => 'Alasan <em>urgent</em>',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesPrioritas()
    {
        return [
            [
                'field' => 'alasan_prioritas',
                'label' => 'Alasan prioritas',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesRencanaJenisPembiusan()
    {
        return [
            [
                'field' => 'rencana_jenis_pembiusan_lain',
                'label' => 'Jenis pembiusan lainnya',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function ambil($nokun)
    {
        $this->db->select(
            'wl.id id_waiting_list, sv.diagnosa_medis, sv.rencana_tindakan_operasi, sv.perkiraan_lama_operasi,
            sv.created_at'
        );
        $this->db->from('medis.tb_pendaftaran_operasi sv');
        $this->db->join('medis.tb_waiting_list_operasi wl', 'wl.nokun = sv.nokun', 'left');
        $this->db->where('sv.nokun', $nokun);
        $this->db->order_by($this->_order_by, $this->_order_by_type);
        $query = $this->db->get();
        return $query->row_array();
    }

    public function alasanSifatOperasi($sifatOperasi, $smf = 0)
    {
        $this->db->select('id, deskripsi');
        $this->db->from('db_master.tb_alasan_sifat_operasi');
        $this->db->where('status', 1);
        $this->db->where('id_sifat_operasi', $sifatOperasi);
        $this->db->where('id_smf', $smf);
        $this->db->order_by('urutan', 'ASC');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function simpan($data)
    {
        $this->db->insert($this->_table, $data);
        return $this->db->insert_id();
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_pendaftaran_operasi.id', $id);
        $this->db->update($this->_table, $data);
    }

    public function history($norm)
    {
        $this->db->select(
            "sv.id, sv.nokun NOKUN, CONCAT(sv.tanggal_operasi, ', ', sv.jam_operasi) TANGGAL_JAM_OPERASI,
            master.getNamaLengkapPegawai(peng.NIP) USER, master.getNamaLengkapPegawai(dpjp.NIP) DPJP,
            rk.DESKRIPSI RUANGAN, p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN, sv.created_at TANGGAL_DAFTAR,
            (
                SELECT CONCAT(po.tgl_operasi, ' ', po.waktu_operasi)
                FROM medis.tb_waiting_list_operasi wlo
                LEFT JOIN perjanjian.penjadwalan_operasi po ON po.id_waiting_list_operasi = wlo.id AND po.status != 0
                WHERE wlo.id_pendaftaran_operasi = sv.id
                AND po.tgl_operasi IS NOT NULL
                ORDER BY po.tgl_operasi ASC, po.waktu_operasi ASC
                LIMIT 1
            ) AS TANGGAL_PERJANJIAN"
        );
        $this->db->from('medis.tb_pendaftaran_operasi sv');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = sv.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
        $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'left');
        $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'left');
        $this->db->join('master.dokter dpjp', 'dpjp.ID = sv.dokter_bedah', 'left');
        $this->db->join('master.ruangan rk', 'rk.ID = pk.RUANGAN', 'left');
        $this->db->join('aplikasi.pengguna peng', 'peng.ID = sv.oleh', 'left');
        $this->db->where('sv.status', 1);
        $this->db->where('p.NORM', $norm);
        $this->db->order_by($this->_order_by, $this->_order_by_type);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function detail($id)
    {
        $this->db->select(
            'sv.nokun, wl.id id_waiting_list, wl.norm, sv.diagnosa_medis, sv.rencana_tindakan_operasi, sv.ruang_tujuan,
            sv.ruang_operasi, sv.perkiraan_lama_operasi, wlor.tanggal_rencana tanggal_operasi, sv.jam_operasi, sv.tujuan_operasi,
            sv.sifat_operasi, sv.sifat_operasi_lain id_alasan_cito, ac.deskripsi alasan_cito,
            sv.alasan_urgent id_alasan_urgent, au.deskripsi alasan_urgent, sv.alasan_prioritas id_alasan_prioritas,
            ap.deskripsi alasan_prioritas, sv.rencana_jenis_pembiusan, sv.rencana_jenis_pembiusan_lain,
            sv.dokter_bedah, sv.potong_beku, sv.join_operasi, sv.catatan_khusus, sv.kelas, wlor.keterangan keterangan_rencana, sv.tindakan_operasi, sv.pilihan_op'
        );
        $this->db->from('medis.tb_pendaftaran_operasi sv');
        $this->db->join('medis.tb_waiting_list_operasi wl', 'wl.id_pendaftaran_operasi = sv.id', 'left');
        $this->db->join('medis.tb_waiting_list_operasi_rencana wlor', 'wlor.id_wlo = wl.id', 'left');
        $this->db->join('master.dokter d', 'd.ID = sv.dokter_bedah', 'left');
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join( // CITO
            'db_master.tb_alasan_sifat_operasi ac',
            'ac.id = sv.sifat_operasi_lain AND ac.id_sifat_operasi AND ac.id_smf = p.smf',
            'left'
        );
        $this->db->join( //Urgent
            'db_master.tb_alasan_sifat_operasi au',
            'au.id = sv.alasan_urgent AND au.id_sifat_operasi AND au.id_smf = p.smf',
            'left'
        );
        $this->db->join( // Prioritas
            'db_master.tb_alasan_sifat_operasi ap',
            'ap.id = sv.alasan_prioritas AND ap.id_sifat_operasi AND ap.id_smf = p.smf',
            'left'
        );
        $this->db->where(
            'wlor.id = (
                SELECT MAX(wlor.id)
                FROM medis.tb_waiting_list_operasi_rencana wlor
                WHERE wlor.id_wlo = wl.id
            )'
        );
        $this->db->where('sv.id', $id);
        $query = $this->db->get();
        return $query->row_array();
    }

    /**
     * Method untuk mendapatkan nama lengkap pasien
     */
    public function getNamaLengkapPasien($norm)
    {
        $query = $this->db->query("SELECT master.getNamaLengkap(?) AS nama_pasien", array($norm));
        $result = $query->row();
        return $result ? $result->nama_pasien : null;
    }

    /**
     * Method untuk mendapatkan kontak pasien
     */
    public function getKontakPasien($norm)
    {
        $query = $this->db->query("SELECT db_rekammedis.getKontakPasien(?) AS nomor", array($norm));
        $result = $query->row();
        return $result ? $result->nomor : null;
    }


    /**
     * Method untuk generate nomor kontrol
     * Menggunakan prepared statement untuk keamanan dan stabilitas
     */
    public function generateNoKontrol($tanggal)
    {
        // Gunakan query langsung dengan parameter binding untuk menghindari masalah sinkronisasi
        $query = $this->db->query("SELECT generator.generateNoKontrol(?) AS KODE", array($tanggal));
        $result = $query->row();

        // Pastikan result di-free untuk membersihkan koneksi
        if ($query) {
            $query->free_result();
        }

        return $result ? $result->KODE : null;
    }
   


    /**
     * Method untuk cek status rawat inap berdasarkan NOPEN
     */
    public function cekStatusRawatInap($nopen)
    {
        $this->db->select('a.RUANGAN');
        $this->db->from('pendaftaran.tujuan_pasien a');
        $this->db->join('master.ruangan b', 'b.ID = a.RUANGAN');
        $this->db->group_start();
            $this->db->where('b.jenis', 5);
            $this->db->where_in('b.jenis_kunjungan', [3, 6]);
        $this->db->group_end();
        $this->db->where('a.NOPEN', $nopen);
        $query = $this->db->get();
        return ($query->num_rows() > 0) ? true : false;
    }

    /**
     * Method untuk insert data ke tabel remun_medis.perjanjian
     * Khusus untuk ruang operasi ID=16
     */
    public function insertPerjanjianOperasi($data)
    {
        $this->db->insert('remun_medis.perjanjian', $data);
        return $this->db->insert_id();
    }

    /**
     * Method untuk generate kode booking
     */
    public function ambil_kode_booking()
    {
        $query = $this->db->query('SELECT db_reservasi.generateNoReservasiAdm() AS kode_booking');
        return $query->row();
    }

    /**
     * Method untuk insert data ke tabel db_reservasi.tb_reservasi
     */
    public function insertReservasi($data)
    {
        $this->db->insert('db_reservasi.tb_reservasi', $data);
        return $this->db->insert_id();
    }

    /**
     * Method untuk mengambil data reservasi berdasarkan ID pendaftaran operasi
     * Join antara medis.tb_pendaftaran_operasi, remun_medis.perjanjian, dan db_reservasi.tb_reservasi
     */
    public function getDataReservasi($id_pendaftaran_operasi)
    {
        $this->db->select('
            c.id as id_pendaftaran,
            a.ID as id_perjanjian,
            b.id as id_reservasi,
            a.NOMR as norm,
            a.NAMAPASIEN as nama_pasien,
            db_rekammedis.getNamaLengkapDokter(a.ID_DOKTER) AS nama_dokter,
            b.kode_booking,
            DATE_FORMAT(a.TANGGAL, "%d/%m/%Y") as tanggal_operasi,
            a.DIAGNOSA as diagnosis,
            a.TINDAKAN as tindakan,
            b.id_cara_bayar,
            b.id_kelas,
            cb.DESKRIPSI as cara_bayar_text,
            kr.DESKRIPSI as kelas_text,
            b.tgl_rencanaMasuk,
            c.tanggal_operasi
        ');
        $this->db->from('medis.tb_pendaftaran_operasi c');
        $this->db->join('remun_medis.perjanjian a', 'a.ID_PENDAFTARAN_OPERASI = c.id', 'left');
        $this->db->join('db_reservasi.tb_reservasi b', 'b.id_perjanjian = a.ID', 'left');
        $this->db->join('master.referensi cb', 'cb.ID = b.id_cara_bayar AND cb.JENIS = 10', 'left');
        $this->db->join('master.referensi kr', 'kr.ID = b.id_kelas AND kr.JENIS = 19', 'left');
        $this->db->where('c.id', $id_pendaftaran_operasi);
        $query = $this->db->get();
        return $query->row_array();
    }

    /**
     * Method untuk update data reservasi (id_cara_bayar atau id_kelas)
     * Update ke tabel db_reservasi.tb_reservasi
     */
    public function updateReservasi($id_reservasi, $field, $value)
    {
        $data = array($field => $value);
        $this->db->where('id', $id_reservasi);
        $this->db->update('db_reservasi.tb_reservasi', $data);
        return $this->db->affected_rows() > 0;
    }

    /**
     * Method untuk update history tabel dengan data kode_booking
     * Menambahkan kode_booking ke dalam query history untuk menentukan kapan button muncul
     */
    public function historyWithReservasi($norm)
    {
        // $this->db->select(
        //     "sv.id, sv.nokun NOKUN, CONCAT(sv.tanggal_operasi, ', ', sv.jam_operasi) TANGGAL_JAM_OPERASI,
        //     master.getNamaLengkapPegawai(peng.NIP) USER, master.getNamaLengkapPegawai(dpjp.NIP) DPJP,
        //     rk.DESKRIPSI RUANGAN, p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN, sv.created_at TANGGAL_DAFTAR,
        //     res.kode_booking,
        //     (
        //         SELECT CONCAT(po.tgl_operasi, ' ', po.waktu_operasi)
        //         FROM medis.tb_waiting_list_operasi wlo
        //         LEFT JOIN perjanjian.penjadwalan_operasi po ON po.id_waiting_list_operasi = wlo.id AND po.status != 0
        //         WHERE wlo.id_pendaftaran_operasi = sv.id
        //         AND po.tgl_operasi IS NOT NULL
        //         ORDER BY po.tgl_operasi ASC, po.waktu_operasi ASC
        //         LIMIT 1
        //     ) AS TANGGAL_PERJANJIAN"
        // );
        // $this->db->from('medis.tb_pendaftaran_operasi sv');
        // $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = sv.nokun', 'left');
        // $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
        // $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'left');
        // $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'left');
        // $this->db->join('master.dokter dpjp', 'dpjp.ID = sv.dokter_bedah', 'left');
        // $this->db->join('master.ruangan rk', 'rk.ID = pk.RUANGAN', 'left');
        // $this->db->join('aplikasi.pengguna peng', 'peng.ID = sv.oleh', 'left');
        
        // // Join untuk mendapatkan kode_booking dari reservasi
        // $this->db->join('remun_medis.perjanjian perj', 'perj.ID_PENDAFTARAN_OPERASI = sv.id', 'left');
        // // $this->db->join('db_rekammedis.tb_reservasi res', 'res.id_perjanjian = perj.ID', 'left');    
        // $this->db->join('db_reservasi.tb_reservasi res', 'res.id_perjanjian = perj.ID', 'left');
        // // $this->db->join('db_reservasi.tb_reservasi res', 'res.id_pendaftaran_operasi = sv.ID', 'left');
        
        // $this->db->where('sv.status', 1);
        // $this->db->where('p.NORM', $norm);
        $this->db->select("
            sv.id, 
            sv.nokun AS NOKUN, 
            CONCAT(sv.tanggal_operasi, ', ', sv.jam_operasi) AS TANGGAL_JAM_OPERASI,
            master.getNamaLengkapPegawai(peng.NIP) AS USER, 
            db_rekammedis.getNamaLengkapDokter(sv.dokter_bedah) AS DPJP,
            rk.DESKRIPSI AS RUANGAN, 
            p.NORM, 
            master.getNamaLengkap(p.NORM) AS NAMA_PASIEN,
            sv.created_at TANGGAL_DAFTAR,
            res.kode_booking,
            CONCAT(
                COALESCE(po.tgl_operasi, perj.TANGGAL), ' ',
                COALESCE(po.waktu_operasi, sv.jam_operasi)
            ) AS TANGGAL_PERJANJIAN
        ", false);

        $this->db->from('medis.tb_pendaftaran_operasi sv');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = sv.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
        $this->db->join('master.ruangan rk', 'rk.ID = pk.RUANGAN', 'left');
        $this->db->join('aplikasi.pengguna peng', 'peng.ID = sv.oleh', 'left');
        $this->db->join(
            'remun_medis.perjanjian perj',
            "perj.ID_PENDAFTARAN_OPERASI = sv.id AND perj.ID_RUANGAN = '105090104'",
            'left'
        );
        $this->db->join(
            'db_reservasi.tb_reservasi res',
            "res.id_perjanjian = perj.ID AND res.status != 0",
            'left'
        );
        $this->db->join(
            'perjanjian.penjadwalan_operasi po',
            'po.id_perjanjian = perj.id',
            'left'
        );

        $this->db->where('sv.status', 1);
        $this->db->where('p.NORM', $norm);
        $this->db->where('perj.ID_RUANGAN', '105090104');
        $this->db->order_by('sv.created_at', 'DESC');
        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Method untuk update TANGGALRAWATINAP di remun_medis.perjanjian
     * Berdasarkan id_reservasi dari db_reservasi.tb_reservasi
     */
    public function updateTanggalRawatInap($id_pendaftaran, $tanggal)
    {
        $data = array('TANGGALRAWATINAP' => $tanggal);
        $this->db->where('id_pendaftaran_operasi', $id_pendaftaran);
        $this->db->update('remun_medis.perjanjian', $data);        
        
        return $this->db->affected_rows();
    }

    /**
     * Method untuk update tanggal_operasi di medis.tb_pendaftaran_operasi
     */
    public function updateTanggalOperasiPendaftaran($id_pendaftaran, $tanggal_operasi)
    {
        $data = array('tanggal_operasi' => $tanggal_operasi);
        $this->db->where('id', $id_pendaftaran);
        $this->db->update('medis.tb_pendaftaran_operasi', $data);
        return $this->db->affected_rows();
    }

    /**
     * Method untuk update TANGGAL di remun_medis.perjanjian
     */
    public function updateTanggalOperasiPerjanjian($id_pendaftaran, $tanggal_operasi)
    {
        $data = array('TANGGAL' => $tanggal_operasi);
        $this->db->where('ID_PENDAFTARAN_OPERASI', $id_pendaftaran);
        $this->db->update('remun_medis.perjanjian', $data);
        return $this->db->affected_rows();
    }
        
}

/* End of file PengkajianDafOpeModel.php */
/* Location: ./application/models/operasi/PengkajianDafOpeModel.php */
