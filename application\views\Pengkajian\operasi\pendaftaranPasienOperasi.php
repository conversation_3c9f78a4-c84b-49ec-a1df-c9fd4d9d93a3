<ul class="nav nav-tabs nav-justified">
    <?php if (isset($getNomr['GEDUNG']) && $getNomr['GEDUNG'] == 1): ?>
    <li class="nav-item">
        <a href="#tab-perjanjian-operasi" data-toggle="tab" aria-expanded="false" class="nav-link">
            Daftar Perjanjian Operasi
        </a>
    </li>
    <?php endif; ?>
    <li class="nav-item">
        <a href="#tab-form-pendaftaran-pra-operasi" data-toggle="tab" aria-expanded="false" class="nav-link active">
            Form Pendaftaran Pasien Operasi
        </a>
    </li>
    <li class="nav-item">
        <a href="#tab-history-pendaftaran-pra-operasi" data-toggle="tab" aria-expanded="true" class="nav-link">
            History Pendaftaran Pasien Operasi
        </a>
    </li>
</ul>

<div class="tab-content">
    <!-- <PERSON><PERSON> form -->
    <div role="tabpanel" class="tab-pane fade show active" id="tab-form-pendaftaran-pra-operasi">
        <form id="form-pendaftaran-pra-operasi" autocomplete="off">
            <input type="hidden" name="norm" value="<?= $getNomr['NORM'] ?>">
            <input type="hidden" name="dpjp" value="<?= $getNomr['ID_DOKTER'] ?>">
            <input type="hidden" name="nopen" value="<?= $getNomr['NOPEN'] ?>">
            <input type="hidden" name="nokun" value="<?= $getNomr['NOKUN'] ?>">
            <input type="hidden" name="usia" value="<?= $getNomr['USIA'] ?>">
            <input type="hidden" name="ruang_tujuan" value="<?= $getNomr['ID_RUANGAN'] ?>">
            <input type="hidden" name="jenis_kunjungan" value="<?= $getNomr['JENIS_KUNJUNGAN'] ?>">
            <input type="hidden" name="idemr" value="<?= $this->uri->segment(7) ?>">
            <input type="hidden" name="slot_operasi" id="slot-operasi-pendaftaran-pra-operasi" value="">
            <input type="hidden" name="id_kamar" id="id-kamar-pendaftaran-pra-operasi" value="">
            <?php if (isset($dataSebelumnya['created_at'])): ?>
                <input type="hidden" name="id_waiting_list" id="id-wl-pendaftaran-pra-operasi" value="<?= $dataSebelumnya['id_waiting_list'] ?? 0 ?>">
            <?php endif ?>
            <!-- Mulai dokter bedah -->
            <div class="row form-group">
                <label for="dokter-bedah-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Dokter Bedah Utama
                </label>
                <div class="col-md-8">
                    <select name="dokter_bedah[]" id="dokter-bedah-pendaftaran-pra-operasi" class="form-control">
                        <option value=""></option>
                        <?php foreach ($listDr as $ld): ?>
                            <option id="dokter-bedah-pendaftaran-pra-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>" data-smf="<?= $ld['ID_SMF'] ?>">
                                <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                            </option>
                        <?php endforeach ?>
                    </select>
                </div>
            </div>
            <!-- Akhir dokter bedah -->
            <!-- Mulai rencana tindakan operasi -->
            <div class="row form-group">
                <label for="rencana-tindakan-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Rencana Tindakan Operasi <br>
                    <small>(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                </label>
                <div class="col-md-8">
                    <textarea id="rencana-tindakan-pendaftaran-pra-operasi" name="rencana_tindakan_operasi[]" class="form-control" placeholder="[ Tuliskan Rencana Tindakan ]"><?= $dataPengkajianPraOperasi['rencana_tindakan_operasi'] ?? null ?></textarea>
                </div>
            </div>
            <!-- Akhir rencana tindakan operasi -->
            <!-- Mulai join operasi -->
            <div class="row form-group">
                <label for="join-operasi-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    <em>Join</em> Operasi
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($joinOperasi as $jo): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" name="join_operasi" id="join-operasi-pendaftaran-pra-operasi<?= $jo['id_variabel'] ?>" class="join-operasi-pendaftaran-pra-operasi" value="<?= $jo['id_variabel'] ?>" <?= $jo['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="join-operasi-pendaftaran-pra-operasi<?= $jo['id_variabel'] ?>">
                                        <?= $jo['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir join operasi -->
            <!-- Mulai form dokter bedah lain -->
            <div class="card new-card form-group" id="form-dokter-bedah-lain-pendaftaran-pra-operasi">
                <div class="card-header new-card-header">Dokter Bedah Lain dan Tindakannya</div>
                <div class="card-body">
                    <!-- Mulai dokter bedah lain -->
                    <div class="row form-group">
                        <label for="dokter-lain-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                            Dokter Bedah Lain
                        </label>
                        <div class="col-md-8">
                            <select id="dokter-lain-pendaftaran-pra-operasi" class="form-control">
                                <option value=""></option>
                                <?php foreach ($listDr as $ld): ?>
                                    <option id="dokter-lain-pendaftaran-pra-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                                        <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- Akhir dokter bedah lain -->
                    <!-- Mulai rencana tindakan dokter bedah lain -->
                    <div class="row form-group">
                        <label for="rencana-tindakan-lain-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                            Rencana Tindakan Operasi untuk Dokter Lain <br>
                            <small>(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                        </label>
                        <div class="col-md-8">
                            <textarea id="rencana-tindakan-lain-pendaftaran-pra-operasi" class="form-control" placeholder="[ Tuliskan Rencana Tindakan untuk Dokter Lain ]"></textarea>
                        </div>
                    </div>
                    <!-- Akhir rencana tindakan dokter bedah lain -->
                    <!-- Mulai aksi dokter bedah lain -->
                    <div class="row form-group">
                        <!-- <div class="col-sm-6">
                            <button type="button" class="btn btn-outline-danger btn-block waves-effect" id="hapus-dokter-bedah-lain-pendaftaran-pra-operasi">
                                Hapus
                            </button>
                        </div> -->
                        <div class="col-sm-12">
                            <button type="button" class="btn btn-outline-success btn-block waves-effect" id="tambah-dokter-bedah-lain-pendaftaran-pra-operasi">
                                Tambah
                            </button>
                        </div>
                    </div>
                    <!-- Akhir aksi dokter bedah lain -->
                    <!-- Mulai tabel dokter bedah lain -->
                    <div class="table-responsive overflow-auto">
                        <table class="table table-bordered table-hover table-custom" cellspacing="0" width="100%">
                            <thead>
                                <tr class="table-tr-custom">
                                    <th>#</th>
                                    <th>Dokter Bedah dan Rencana Tindakan Lain</th>
                                </tr>
                            </thead>
                            <tbody id="list-dokter-lain-pendaftaran-pra-operasi"></tbody>
                        </table>
                    </div>
                    <!-- Akhir tabel dokter bedah lain -->
                </div>
            </div>
            <!-- Akhir form dokter bedah lain -->
            <!-- Mulai diagnosis medis -->
            <div class="row form-group">
                <label for="diagnosa-medis-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Diagnosis Medis
                </label>
                <div class="col-md-8">
                    <input type="text" id="diagnosa-medis-pendaftaran-pra-operasi" name="diagnosa_medis" class="form-control" placeholder="[ Diagnosis Medis ]" value="<?= $dataPengkajianPraOperasi['diagnosis_pra_operasi'] ?? null ?>">
                </div>
            </div>
            <!-- Akhir diagnosis medis -->
              <!-- Mulai penjamin/pembiayaan operasi -->
            <div class="row form-group">
                <label for="ruangan-operasi-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Penjamin/Pembiayaan Operasi <span class="text-danger">*</span>
                </label>
                <div class="col-md-8">
                    <select name="ruang_operasi" id="ruangan-operasi-pendaftaran-pra-operasi" class="form-control" required>
                        <option value=""></option>
                        <?php
                        $penjaminOperasi = $this->masterModel->referensiSimpel(81);
                        foreach ($penjaminOperasi as $po):
                            if ($po['ID'] == 2 || $po['ID'] == 16):
                        ?>
                            <option value="<?= $po['ID'] ?>"><?= $po['DESKRIPSI'] ?></option>
                        <?php
                            endif;
                        endforeach;
                        ?>
                    </select>
                </div>
            </div>
            <!-- Akhir penjamin/pembiayaan operasi -->
             <!-- Mulai tanggal -->
            <div class="row form-group">
                <label for="tanggal-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Tanggal
                </label>
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="date" class="form-control" id="tanggal-pendaftaran-pra-operasi" name="tanggal_operasi">
                        <div class="input-group-append">
                            <span class="input-group-text" style="cursor: pointer;" id="btn-calendar-modal" title="Lihat jadwal operasi">
                                <i class="fa fa-calendar"></i>
                            </span>
                        </div>
                    </div>
                    <!-- Keterangan jumlah operasi id=16 -->
                    <div id="keterangan-jumlah-operasi" class="mt-2" style="display: none;">
                        <span class="text-danger" style="font-size:1em;font-weight:bold;cursor:pointer;" id="link-jumlah-operasi">
                            <i class="fa fa-info-circle"></i> 
                            <span id="text-jumlah-operasi">Jumlah Operasi: 0</span>
                        </span>
                    </div>
                </div>
            </div>
            <!-- Akhir tanggal -->    
            <!-- Mulai waktu -->
            <div class="row form-group">
                <label for="waktu-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Waktu
                </label>
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="form-control" id="waktu-pendaftaran-pra-operasi" name="jam_operasi">
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fa fa-clock"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Akhir waktu -->
            <!-- Mulai perkiraan lama operasi -->
            <div class="row form-group">
                <label for="lama-operasi-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Perkiraan Lama Operasi
                </label>
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="form-control" id="lama-operasi-pendaftaran-pra-operasi" name="perkiraan_lama_operasi" placeholder="[ Lama Operasi dalam Menit ]" value="<?= $dataPengkajianPraOperasi['perkiraan_lama_operasi'] ?? null ?>">
                        <div class="input-group-append">
                            <span class="input-group-text">menit</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Akhir perkiraan lama operasi -->
           
            <!-- Mulai tujuan operasi -->
            <div class="row form-group">
                <label for="tujuanOperasiDafOpe" class="col-form-label col-md-4">
                    Tujuan Operasi
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($tujuanOperasi as $to): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" name="tujuan_operasi" id="tujuanOperasiDafOpe<?= $to['id_variabel'] ?>" class="tujuanOperasiDafOpe" value="<?= $to['id_variabel'] ?>" <?= $to['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="tujuanOperasiDafOpe<?= $to['id_variabel'] ?>">
                                        <?= $to['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir tujuan operasi -->
            <!-- Mulai sifat operasi -->
            <div class="row form-group">
                <label for="sifatOperasiDafOpe" class="col-form-label col-md-4">
                    Sifat Operasi
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($sifatOperasi as $so): ?>
                            <div class="col-md-6 form-check">
                                <div class="radio radio-primary">
                                    <input type="radio" name="sifat_operasi" id="sifatOperasiDafOpe<?= $so['id_variabel'] ?>" class="sifatOperasiDafOpe" value="<?= $so['id_variabel'] ?>" <?= $so['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="sifatOperasiDafOpe<?= $so['id_variabel'] ?>">
                                        <?= $so['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir sifat operasi -->
            <!-- Mulai alasan CITO -->
            <div class="row form-group d-none" id="form-cito-pendaftaran-pra-operasi">
                <label for="alasan-cito-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Alasan CITO
                </label>
                <div class="col-md-8">
                    <select name="sifat_operasi_lain" id="alasan-cito-pendaftaran-pra-operasi" class="form-controller">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- Akhir alasan CITO -->
            <!-- Mulai alasan urgent -->
            <div class="row form-group d-none" id="form-urgent-pendaftaran-pra-operasi">
                <label for="alasan-urgent-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Alasan <em>Urgent</em>
                </label>
                <div class="col-md-8">
                    <select name="alasan_urgent" id="alasan-urgent-pendaftaran-pra-operasi" class="form-controller">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- Akhir alasan urgent -->
            <!-- Mulai alasan prioritas -->
            <div class="row form-group d-none" id="form-prioritas-pendaftaran-pra-operasi">
                <label for="alasan-prioritas-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Alasan Prioritas
                </label>
                <div class="col-md-8">
                    <select name="alasan_prioritas" id="alasan-prioritas-pendaftaran-pra-operasi" class="form-controller">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- Akhir alasan prioritas -->
            <!-- Mulai rencana jenis pembiusan -->
            <div class="row form-group">
                <label for="rencanaJenisPembiusanDafOpe" class="col-form-label col-md-4">
                    Rencana Jenis Pembiusan
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($rencanaJenisPembiusan as $renJenPem): ?>
                            <div class="col-md-6 form-check">
                                <div class="radio radio-primary">
                                    <input type="radio" name="rencana_jenis_pembiusan" id="rencanaJenisPembiusanDafOpe<?= $renJenPem['id_variabel'] ?>" class="rencanaJenisPembiusanDafOpe" value="<?= $renJenPem['id_variabel'] ?>" <?= $renJenPem['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="rencanaJenisPembiusanDafOpe<?= $renJenPem['id_variabel'] ?>">
                                        <?= $renJenPem['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir rencana jenis pembiusan -->
            <!-- Mulai rencana jenis pembiusan lain -->
            <div class="row form-group d-none" id="ShowrencanaJenisPembiusan">
                <label for="rencanaJenisPembiusan_lainnya" class="col-form-label col-md-4">
                    Jenis Pembiusan Lainnya
                </label>
                <div class="col-md-8">
                    <textarea id="rencanaJenisPembiusan_lainnya" name="rencana_jenis_pembiusan_lain" class="form-control" placeholder="[ Jelaskan Lainnya ]"></textarea>
                </div>
            </div>
            <!-- Akhir rencana jenis pembiusan lain -->
            <!-- Mulai potong beku -->
            <div class="row form-group">
                <label for="potong-beku-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Potong Beku
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($potongBeku as $pb): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" name="potong_beku" id="potong-beku-pendaftaran-pra-operasi<?= $pb['id_variabel'] ?>" class="potong-beku-pendaftaran-pra-operasi" value="<?= $pb['id_variabel'] ?>" <?= $pb['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="potong-beku-pendaftaran-pra-operasi<?= $pb['id_variabel'] ?>">
                                        <?= $pb['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir potong beku -->
            <!-- Mulai catatan khusus -->
            <div class="row form-group">
                <label for="catatan-khusus-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Catatan Khusus
                </label>
                <div class="col-md-8">
                    <textarea id="catatan-khusus-pendaftaran-pra-operasi" name="catatan_khusus" class="form-control" placeholder="[ Tuliskan catatan khusus]"></textarea>
                </div>
            </div>
            <!-- Akhir catatan khusus -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="pull-right">
                        <button type="submit" class="btn btn-primary waves-effect" id="simpan-pendaftaran-pra-operasi">
                            <i class="fa fa-save"></i> Simpan
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- Akhir form -->
    <!-- Mulai tabel -->
    <div role="tabpanel" class="tab-pane fade" id="tab-history-pendaftaran-pra-operasi">
        <div class="row">
            <div class="col-12">
                <div class="table-responsive">
                    <table id="tabel-pendaftaran-pra-operasi" class="table table-bordered table-hover dt-responsive table-custom" cellspacing="0" width="100%">
                        <thead>
                            <tr class="table-tr-custom">
                                <th>No.</th>
                                <th>Waktu Operasi</th>
                                <th>Jadwal Operasi (OK)</th>
                                <th>Ruang</th>
                                <th>Dokter Bedah Utama</th>
                                <th>Oleh</th>
                                <th>Tanggal Daftar</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                                $no = 1;
                                if (isset($getNomr['GEDUNG']) && $getNomr['GEDUNG'] == 1) {
                                    $dataOperasi = $historydaftaroperasiReservasi;
                                } else {
                                    $dataOperasi = $historydaftaroperasi;
                                }
                            foreach ($dataOperasi as $hprop):
                                ?>
                                <tr>
                                    <td><?= $no ?>.</td>
                                    <td><?= $hprop['TANGGAL_JAM_OPERASI'] != '0000-00-00, 00:00:00' ? date('d/m/Y, H.i.s', strtotime($hprop['TANGGAL_JAM_OPERASI'])) : '-' ?></td>
                                    <td>
                                        <?php
                                            if (!empty($hprop['TANGGAL_PERJANJIAN']) && $hprop['TANGGAL_PERJANJIAN'] != '0000-00-00 00:00:00') {
                                                echo date('d/m/Y, H:i', strtotime($hprop['TANGGAL_PERJANJIAN']));
                                            } else {
                                                echo '-';
                                            }
                                        ?>
                                    </td>
                                    <td><?= $hprop['RUANGAN'] ?></td>
                                    <td><?= $hprop['DPJP'] ?></td>
                                    <td><?= $hprop['USER'] ?></td>
                                    <td><?= isset($hprop['TANGGAL_DAFTAR']) && $hprop['TANGGAL_DAFTAR'] ? date('d/m/Y, H:i', strtotime($hprop['TANGGAL_DAFTAR'])) : '-' ?> </td>

                                    <td>
                                        <a href="#ubah-pendaftaran-pra-operasi" data-toggle="modal" data-backdrop="static" data-id="<?= $hprop['id'] ?>" data-nokun="<?= $hprop['NOKUN'] ?>" class="btn btn-primary btn-block waves-effect mb-1">
                                            <i class="fas fa-edit"></i> Ubah
                                        </a>
                                        <!-- <pre><?php var_dump($hprop['kode_booking'] ?? '(kode_booking tidak ada)'); ?></pre> -->
                                        
                                        <?php if (!empty($hprop['kode_booking']) && $this->session->userdata('profesi') == 6): ?>
                                        <a href="#modal-lengkapi-reservasi" data-toggle="modal" data-backdrop="static" data-id="<?= $hprop['id'] ?>" class="btn btn-success btn-block waves-effect btn-lengkapi-reservasi">
                                            <i class="fas fa-calendar-check"></i> Lengkapi Reservasi
                                        </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php
                                $no++;
                            endforeach;
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Akhir tabel -->

    <?php if (isset($getNomr['GEDUNG']) && $getNomr['GEDUNG'] == 1): ?>
    <!-- Mulai Tab Perjanjian Operasi -->
    <div role="tabpanel" class="tab-pane fade" id="tab-perjanjian-operasi">
        <!-- Filter Section -->
        <div class="card mb-3">
            <div class="card-body">
                <!-- Filter Rencana Tanggal Operasi -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Daftar Rencana Tanggal Operasi</label>
                        <div class="row">
                            <div class="col-md-6">
                                <select id="filter-tanggal-dari" class="form-control" placeholder="Dari Tanggal">
                                    <option value="">[ Dari Tanggal ]</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <select id="filter-tanggal-sampai" class="form-control" placeholder="Sampai Tanggal">
                                    <option value="">[ Sampai Tanggal ]</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filter Me/Perjanjian/Penjadwalan -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="btn-group" role="group" id="filter-status-group">
                            <button type="button" class="btn btn-filter-me" data-filter="me">Me</button>
                            <button type="button" class="btn btn-filter-perjanjian" data-filter="perjanjian">Perjanjian</button>
                            <button type="button" class="btn btn-filter-penjadwalan" data-filter="penjadwalan">Penjadwalan</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTable Section -->
        <div class="row">
            <div class="col-12">
                <div class="table-responsive">
                    <table id="tabel-perjanjian-operasi" class="table table-bordered table-hover dt-responsive table-custom" cellspacing="0" width="100%">
                        <thead>
                            <tr class="table-tr-custom">
                                <th>No</th>
                                <th>Tanggal Operasi</th>
                                <th>Nama Pasien</th>
                                <th>Dokter Bedah</th>
                                <th>Jam Operasi</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data akan diisi via Ajax -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Akhir Tab Perjanjian Operasi -->
    <?php endif; ?>
</div>

<!-- Mulai modal ubah daftar pra operasi -->
<div aria-hidden="true" aria-labelledby="mySmallModalLabel" class="modal fade" id="ubah-pendaftaran-pra-operasi" role="dialog" style="display: none;">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content" id="hasilDaftarPraOperasi"></div>
    </div>
</div>
<!-- Akhir modal ubah daftar pra operasi -->

<!-- Mulai modal detail operasi harian (untuk tanggal spesifik) -->
<div class="modal fade" id="modal-detail-operasi-harian" tabindex="-1" role="dialog" aria-labelledby="modalDetailOperasiHarianLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalDetailOperasiHarianLabel">
                    <i class="fa fa-calendar-day"></i> Detail Operasi Harian - Gedung C
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 
                            Menampilkan jadwal operasi untuk tanggal <strong id="tanggal-modal-operasi-harian"></strong>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table id="tabel-detail-operasi-harian" class="table table-bordered table-hover table-striped dt-responsive" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th width="8%">No</th>
                                <th width="12%">Waktu Mulai</th>
                                <th width="12%">Waktu Selesai</th>
                                <th width="35%">Tindakan</th>
                                <th width="23%">Dokter</th>
                                <th width="10%">Ruangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data akan diisi via server-side processing -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir modal detail operasi harian -->

<!-- Mulai modal detail operasi range (untuk 30 hari) -->
<div class="modal fade" id="modal-detail-operasi-range" tabindex="-1" role="dialog" aria-labelledby="modalDetailOperasiRangeLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalDetailOperasiRangeLabel">
                    <i class="fa fa-calendar-alt"></i> Jadwal Operasi 30 Hari - Gedung C
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 
                            Menampilkan jadwal operasi untuk periode <strong id="tanggal-modal-operasi-range"></strong>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table id="tabel-detail-operasi-range" class="table table-bordered table-hover table-striped dt-responsive" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th width="6%">No</th>
                                <th width="12%">Tanggal</th>
                                <th width="10%">Waktu Mulai</th>
                                <th width="10%">Waktu Selesai</th>
                                <th width="35%">Tindakan</th>
                                <th width="18%">Dokter</th>
                                <th width="9%">Ruangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data akan diisi via server-side processing -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir modal detail operasi range -->

<!-- Mulai modal lengkapi reservasi -->
<div class="modal fade" id="modal-lengkapi-reservasi" tabindex="-1" role="dialog" aria-labelledby="modalLengkapiReservasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalLengkapiReservasiLabel">
                    <i class="fa fa-calendar-check"></i> Lengkapi Reservasi Operasi
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form-lengkapi-reservasi">
                    <input type="hidden" id="reservasi-id-pendaftaran" name="id_pendaftaran">
                    <input type="hidden" id="reservasi-id-reservasi" name="id_reservasi">
                    
                     <!-- Data Editable -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fa fa-edit"></i> Data Reservasi</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Cara Bayar <span class="text-danger">*</span></label>
                                        <select id="reservasi-cara-bayar" class="form-control" required>
                                            <option value="">[ Pilih Cara Bayar ]</option>
                                            <?php foreach($caraBayarOptions as $cb): ?>
                                                <option value="<?= $cb['ID'] ?>"><?= $cb['DESKRIPSI'] ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Kelas Rawat <span class="text-danger">*</span></label>
                                        <select id="reservasi-kelas-rawat" class="form-control" required>
                                            <option value="">[ Pilih Kelas Rawat ]</option>
                                            <?php foreach($kelasRawatOptions as $kr): ?>
                                                <option value="<?= $kr['ID'] ?>"><?= $kr['DESKRIPSI'] ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Tanggal Rencana Masuk</label>
                                        <input type="date" id="reservasi-tgl-rencana-masuk" class="form-control">
                                    </div>
                                </div>
                                 <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Tanggal Perjanjian Operasi</label>
                                        <input type="date" id="reservasi-tanggal-operasi-update" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Data Readonly -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fa fa-info-circle"></i> Informasi Pasien & Operasi</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Nomor RM & Nama Pasien</label>
                                        <input type="text" id="reservasi-norm-nama" class="form-control" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Dokter</label>
                                        <input type="text" id="reservasi-dokter" class="form-control" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Kode Booking</label>
                                        <input type="text" id="reservasi-kode-booking" class="form-control" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Tanggal Operasi</label>
                                        <input type="text" id="reservasi-tanggal-operasi" class="form-control" readonly>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Diagnosis</label>
                                        <textarea id="reservasi-diagnosis" class="form-control" rows="2" readonly></textarea>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Tindakan Operasi</label>
                                        <textarea id="reservasi-tindakan" class="form-control" rows="2" readonly></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                   
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir modal lengkapi reservasi -->

<!-- Modal List Perjanjian Operasi - Light Theme -->
<div class="modal fade" id="modal-list-perjanjian-operasi" tabindex="-1" role="dialog" aria-labelledby="modalListPerjanjianOperasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalListPerjanjianOperasiLabel">
                    <i class="fa fa-calendar-alt mr-2"></i>List Perjanjian Operasi
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Navigasi Bulan/Tahun -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <button type="button" class="btn-light-nav" id="btn-prev-range-perjanjian" disabled>
                                <i class="fa fa-chevron-left"></i>
                            </button>
                            <h4 class="mb-0 text-center" id="current-month-year-perjanjian">
                                <!-- Akan diisi via JavaScript -->
                            </h4>
                            <button type="button" class="btn-light-nav" id="btn-next-range-perjanjian">
                                <i class="fa fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Container untuk 3 hari -->
                <div class="row" id="container-3-hari-perjanjian">
                    <!-- Akan diisi via JavaScript -->
                </div>

                <!-- Loading indicator -->
                <div class="text-center" id="loading-perjanjian" style="display: none;">
                    <i class="fa fa-spinner fa-spin fa-2x" style="color: #007BFF;"></i>
                    <p class="mt-2" style="color: #6c757d;">Memuat data perjanjian operasi...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-light-nav" data-dismiss="modal">
                    <i class="fa fa-times mr-2"></i>Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir Modal List Perjanjian Operasi -->

<!-- CSS untuk Modal List Perjanjian Operasi - Light Theme -->
<style>
    /* Modal styling light theme */
    #modal-list-perjanjian-operasi .modal-content {
        background-color: #FFFFFF;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
    }

    #modal-list-perjanjian-operasi .modal-header {
        background-color: #FFFFFF;
        color: #212529;
        border-radius: 8px 8px 0 0;
        border-bottom: 1px solid #dee2e6;
    }

    #modal-list-perjanjian-operasi .modal-header .modal-title {
        color: #212529 !important;
        font-weight: 600;
    }

    #modal-list-perjanjian-operasi .modal-body {
        background-color: #FAFAFA;
    }

    /* Header bulan/tahun styling untuk light theme */
    #modal-list-perjanjian-operasi #current-month-year-perjanjian {
        color: #212529 !important;
        font-weight: bold;
    }

    /* Tombol navigasi light */
    .btn-light-nav {
        background-color: #FFFFFF;
        border: 2px solid #003366;
        color: #003366;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        font-size: 14px;
        box-shadow: 0 2px 6px rgba(0, 51, 102, 0.3);
    }

    .btn-light-nav:hover:not(:disabled) {
        background-color: #E6F0FF;
        border-color: #0056B3;
        color: #0056B3;
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0, 51, 102, 0.4);
    }

    .btn-light-nav:disabled {
        background-color: #F8F9FA;
        border-color: #E9ECEF;
        color: #6C757D;
        cursor: not-allowed;
        opacity: 0.6;
        box-shadow: none;
    }

    /* Container untuk hari */
    .hari-container {
        border: 2px solid #E9ECEF;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        background-color: #FFFFFF;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .hari-header {
        text-align: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #E9ECEF;
    }

    .hari-nama {
        font-weight: bold;
        font-size: 18px;
        color: #212529;
        margin-bottom: 5px;
    }

    .hari-tanggal {
        font-weight: bold;
        font-size: 24px;
        color: #007BFF;
    }

    /* Grid untuk 8 slot operasi (2 kolom x 4 baris) - fallback */
    .slot-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(4, 1fr);
        gap: 12px;
        min-height: 480px;
    }

    /* Container untuk kamar section */
    .kamar-section {
        margin-bottom: 20px;
        border: 2px solid #E9ECEF;
        border-radius: 6px;
        background-color: #FFFFFF;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .kamar-header {
        /* background-color: #007BFF;
        color: #FFFFFF;
        padding: 10px 15px;
        text-align: center;
        font-size: 14px;
        font-weight: bold;
        border-bottom: 1px solid #0056B3; */
         background-color: #007BFF;
        color: #FFFFFF;
        padding: 10px 15px;
        font-size: 14px;
        font-weight: bold;
        border-bottom: 1px solid #0056B3;

        display: flex;
        justify-content: space-between; /* teks kiri, button kanan */
        align-items: center;            /* vertikal sejajar */
    }

    /* Grid untuk slot per kamar (dinamis berdasarkan jumlah slot) */
    .slot-grid-kamar {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        padding: 12px;
        min-height: 240px;
        background-color: #FAFAFA;
    }

    /* Tombol tambah kuota operasi */
   .btn-tambah-kuota {
    background-color: #28A745;   /* hijau */
    border: none;
    cursor: pointer;
    padding: 6px;
    border-radius: 50%;          /* jadi bulat */
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease, background-color 0.2s ease;
    }

    .btn-tambah-kuota i {
        font-size: 18px;
        color: white;                /* icon putih, biar kontras */
        transition: transform 0.2s ease;
    }

    .btn-tambah-kuota:hover {
        background-color: #218838;   /* hijau lebih gelap */
        transform: scale(1.1);       /* sedikit membesar */
    }

    .btn-tambah-kuota:active {
        transform: scale(0.9);       /* mengecil sebentar saat diklik */
    }


    /* Slot operasi individual */
    .slot-operasi {
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 110px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        position: relative;
        overflow: hidden;
        font-size: 11px;
    }

    /* Slot kosong (belum terisi) - Putih dengan border biru tua */
    .slot-operasi.slot-kosong,
    .slot-operasi.kosong {
        background-color: #FFFFFF;
        border: 2px solid #003366;
        color: #007BFF;
        text-align: center;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(0, 51, 102, 0.3);
    }

    .slot-operasi.slot-kosong:hover,
    .slot-operasi.kosong:hover {
        background-color: #E6F0FF;
        border-color: #0056B3;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 51, 102, 0.4);
    }

    /* Slot terisi tanpa jadwal (baru perjanjian) - Kuning */
    .slot-operasi.slot-perjanjian,
    .slot-operasi.terisi {
        background-color: #FFD54F;
        border: 2px solid #FFA000;
        color: #212529;
        box-shadow: 0 2px 6px rgba(255, 160, 0, 0.3);
    }

    .slot-operasi.slot-perjanjian:hover,
    .slot-operasi.terisi:hover {
        background-color: #FFCA28;
        border-color: #FF8F00;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(255, 160, 0, 0.4);
    }

    /* Slot terisi dengan jadwal - Hijau */
    .slot-operasi.slot-terjadwal {
        background-color: #28A745;
        border: 2px solid #006400;
        color: #FFFFFF;
        /* pointer-events dihilangkan agar tooltip tetap berfungsi */
        cursor: not-allowed !important;
        box-shadow: 0 2px 6px rgba(0, 100, 0, 0.3);
    }

    .slot-operasi.slot-terjadwal:hover {
        background-color: #218838;
        border-color: #004d00;
        box-shadow: 0 3px 8px rgba(0, 100, 0, 0.4);
        transform: none;
    }

    /* Slot selesai - Hijau dengan variasi */
    .slot-operasi.slot-selesai {
        background-color: #28A745;
        border: 2px solid #006400;
        color: #FFFFFF;
        box-shadow: 0 2px 6px rgba(0, 100, 0, 0.3);
    }

    .slot-operasi.slot-selesai:hover {
        background-color: #218838;
        border-color: #004d00;
        box-shadow: 0 3px 8px rgba(0, 100, 0, 0.4);
        transform: translateY(-2px);
    }

    /* Info dalam slot */
    .slot-info {
        font-size: 11px;
        line-height: 1.4;
        width: 100%;
        overflow: hidden;
    }

    .slot-info-item {
        margin-bottom: 4px;
        word-wrap: break-word;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .slot-info-item:last-child {
        margin-bottom: 0;
    }

    .slot-info-label {
        font-weight: bold;
        display: inline-block;
        min-width: fit-content;
    }

    .slot-info-value {
        word-break: break-word;
    }

    /* Slot waktu styling untuk tema terang */
    .slot-waktu {
        font-weight: bold;
        font-size: 14px;
        text-align: center;
        margin-bottom: 6px;
        padding: 4px 8px;
        border-radius: 6px;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    /* Waktu untuk slot kosong */
    .slot-operasi.slot-kosong .slot-waktu,
    .slot-operasi.kosong .slot-waktu {
        background-color: rgba(0, 123, 255, 0.1);
        color: #007BFF;
        border-color: #007BFF;
    }

    /* Waktu untuk slot perjanjian */
    .slot-operasi.slot-perjanjian .slot-waktu,
    .slot-operasi.terisi .slot-waktu {
        background-color: rgba(255, 160, 0, 0.2);
        color: #212529;
        border-color: #FFA000;
    }

    /* Waktu untuk slot terjadwal */
    .slot-operasi.slot-terjadwal .slot-waktu {
        background-color: rgba(255, 255, 255, 0.2);
        color: #FFFFFF;
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Waktu untuk slot selesai */
    .slot-operasi.slot-selesai .slot-waktu {
        background-color: rgba(255, 255, 255, 0.2);
        color: #FFFFFF;
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Slot kosong icon dan text */
    .slot-kosong-icon {
        font-size: 28px;
        color: #007BFF;
        margin-bottom: 8px;
    }

    .slot-kosong-text {
        font-weight: bold;
        font-size: 12px;
        color: #007BFF;
    }

    /* Color untuk info label berdasarkan jenis slot */
    .slot-operasi.slot-kosong .slot-info-label,
    .slot-operasi.kosong .slot-info-label {
        color: #007BFF;
    }

    .slot-operasi.slot-perjanjian .slot-info-label,
    .slot-operasi.terisi .slot-info-label {
        color: #212529;
    }

    .slot-operasi.slot-terjadwal .slot-info-label {
        color: #FFFFFF;
    }

    .slot-operasi.slot-selesai .slot-info-label {
        color: #FFFFFF;
    }

    /* Color untuk info value berdasarkan jenis slot */
    .slot-operasi.slot-kosong .slot-info-value,
    .slot-operasi.kosong .slot-info-value {
        color: #6C757D;
    }

    .slot-operasi.slot-perjanjian .slot-info-value,
    .slot-operasi.terisi .slot-info-value {
        color: #495057;
    }

    .slot-operasi.slot-terjadwal .slot-info-value {
        color: rgba(255, 255, 255, 0.9);
    }

    .slot-operasi.slot-selesai .slot-info-value {
        color: rgba(255, 255, 255, 0.9);
    }

    /* Responsive design */
    @media (max-width: 992px) {
        .slot-grid {
            grid-template-columns: 1fr;
            grid-template-rows: repeat(8, 1fr);
            min-height: 800px;
            gap: 10px;
        }

        .slot-operasi {
            min-height: 90px;
        }

        .slot-info {
            font-size: 10px;
        }

        .slot-waktu {
            font-size: 12px;
        }

        .hari-nama {
            font-size: 16px;
        }

        .hari-tanggal {
            font-size: 20px;
        }
    }

    @media (max-width: 576px) {
        .hari-container {
            padding: 10px;
            margin-bottom: 15px;
        }

        .hari-nama {
            font-size: 14px;
        }

        .hari-tanggal {
            font-size: 18px;
        }

        .slot-operasi {
            min-height: 80px;
            padding: 8px;
        }

        .slot-info {
            font-size: 9px;
        }

        .slot-waktu {
            font-size: 11px;
            padding: 3px 6px;
        }

        .slot-kosong-icon {
            font-size: 24px;
        }

        .slot-kosong-text {
            font-size: 11px;
        }

        .btn-light-nav {
            padding: 6px 12px;
            font-size: 12px;
            min-width: 36px;
        }
    }

    /* Loading state untuk tema terang */
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
    }

    /* Custom scrollbar untuk tema terang */
    #modal-list-perjanjian-operasi ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    #modal-list-perjanjian-operasi ::-webkit-scrollbar-track {
        background-color: #F8F9FA;
        border-radius: 4px;
    }

    #modal-list-perjanjian-operasi ::-webkit-scrollbar-thumb {
        background-color: #CED4DA;
        border-radius: 4px;
    }

    #modal-list-perjanjian-operasi ::-webkit-scrollbar-thumb:hover {
        background-color: #ADB5BD;
    }

    /* CSS untuk Filter Buttons Tab Perjanjian Operasi */
    .btn-filter-me, .btn-filter-perjanjian, .btn-filter-penjadwalan {
        border: 2px solid;
        padding: 8px 16px;
        margin-right: 8px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    /* Me Button - Abu-abu */
    .btn-filter-me {
        background-color: #FFFFFF;
        border-color: #6C757D;
        color: #6C757D;
    }
    .btn-filter-me.active {
        background-color: #6C757D;
        border-color: #6C757D;
        color: #FFFFFF;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
    }

    /* Perjanjian Button - Kuning */
    .btn-filter-perjanjian {
        background-color: #FFFFFF;
        border-color: #FFC107;
        color: #FFC107;
    }
    .btn-filter-perjanjian.active {
        background-color: #FFC107;
        border-color: #FFC107;
        color: #FFFFFF;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }

    /* Penjadwalan Button - Hijau */
    .btn-filter-penjadwalan {
        background-color: #FFFFFF;
        border-color: #28A745;
        color: #28A745;
    }
    .btn-filter-penjadwalan.active {
        background-color: #28A745;
        border-color: #28A745;
        color: #FFFFFF;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }

    /* Hover effects */
    .btn-filter-me:hover:not(.active) {
        background-color: #F8F9FA;
        transform: translateY(-1px);
    }
    .btn-filter-perjanjian:hover:not(.active) {
        background-color: #FFF8E1;
        transform: translateY(-1px);
    }
    .btn-filter-penjadwalan:hover:not(.active) {
        background-color: #E8F5E8;
        transform: translateY(-1px);
    }
</style>

<!-- Include Toastr CSS dan JS -->
<!-- <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script> -->

<script>
    $(document).ready(function() {

        // Fungsi global inisialisasi tooltip aman (hindari konflik jQuery UI vs Bootstrap)
        window.initTooltips = function() {
            var $els = $('[data-toggle="tooltip"]');
            $els.each(function () {
                var $t = $(this);
                // Jika instance Bootstrap tooltip ada
                if ($t.data('bs.tooltip')) {
                    try {
                        $t.tooltip('hide');
                    } catch(e){}
                    $t.removeData('bs.tooltip');
                }
                // Jika instance jQuery UI tooltip ada
                if ($t.data('ui-tooltip')) {
                    try {
                        $t.tooltip('destroy'); // method milik jQuery UI
                    } catch(e){}
                }
            });
            // Inisialisasi ulang hanya dengan opsi Bootstrap (akan diabaikan oleh jQuery UI bila masih override)
            try {
                $els.tooltip({
                    html: false,
                    placement: 'top',
                    container: 'body'
                });
            } catch(e){
                // Fail-safe: diamkan saja
                console.warn('Tooltip init warning:', e);
            }
        };

        // Mulai history
        $('#tabel-pendaftaran-pra-operasi').DataTable({
            responsive: true,
            language: {
                'sEmptyTable': 'Maaf, tidak ada data yang tersedia',
                'sInfo': 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                'sInfoEmpty': 'Menampilkan 0 sampai 0 dari 0 data',
                'sInfoFiltered': '(Pencarian dari _MAX_ total data)',
                'sInfoPostFix': '',
                'sInfoThousands': ',',
                'sLengthMenu': 'Menampilkan _MENU_ data',
                'sLoadingRecords': 'Harap tunggu...',
                'sProcessing': 'Sedang memproses...',
                'sSearch': 'Pencarian:',
                'sZeroRecords': 'Data tidak ditemukan',
                'oPaginate': {
                    'sFirst': 'Pertama',
                    'sLast': 'Terakhir',
                    'sNext': 'Selanjutnya',
                    'sPrevious': 'Sebelumnya'
                }
            },
        });
        // Akhir history

        // Mulai dokter bedah
        let smf = $('#dokter-bedah-pendaftaran-pra-operasi').find(':selected').data('smf');
        $('#dokter-bedah-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih ]'
        }).val("<?= $getNomr['ID_DOKTER'] ?? 0 ?>").trigger('change');

        $('#dokter-bedah-pendaftaran-pra-operasi').change(function() {
            smf = $('#dokter-bedah-pendaftaran-pra-operasi').find(':selected').data('smf');
        });
        // Akhir dokter bedah

        // Mulai dokter bedah lain
        $('#dokter-lain-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih dokter bedah lain ]'
        });
        // Akhir dokter bedah lain

        // Mulai penjamin/pembiayaan operasi
        $('#ruangan-operasi-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih penjamin/pembiayaan ]'
        });

        // Auto-select penjamin/pembiayaan operasi berdasarkan RUANGAN/GEDUNG pasien
        var id_ruangan_pasien = "<?= $getNomr['ID_RUANGAN'] ?>";

        // Jika ID_RUANGAN ada
        if (id_ruangan_pasien) {
            // Panggil AJAX untuk mendapatkan detail ruangan
            $.ajax({
                url: "<?= base_url('operasi/PengkajianDafOpe/cekRuangan') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    id_ruangan: id_ruangan_pasien
                },
                success: function(response) {
                    if (response) {
                        // Jika GEDUNG is NULL, pilih Operasi Reguler (ID=2)
                        if (response.GEDUNG === null) {
                            $('#ruangan-operasi-pendaftaran-pra-operasi').val('2').trigger('change');
                        }
                        // Jika GEDUNG == 1, pilih Operasi Swasta (Gedung C) (ID=16)
                        else if (response.GEDUNG == 1) {
                            $('#ruangan-operasi-pendaftaran-pra-operasi').val('16').trigger('change');
                        }
                    }
                }
            });
        }
        // Akhir ruangan operasi

        // Fungsi untuk menambah dokter bedah lain
        function tambahDokterBedahLain() {
            let jumlah = $('.hapus-item-dokter-lain').length;
            let id_dokter = $('#dokter-lain-pendaftaran-pra-operasi').val();
            let dokter = $('#dokter-lain-pendaftaran-pra-operasi option:selected').text().trim();
            let tindakan = $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val();
            let isi = null;

            // Mulai periksa
            if (jumlah <= 4) { // Periksa jumlah apakah sudah ada 5
                if (id_dokter !== '' && tindakan !== '') { // Periksa apakah sudah diisi
                    // Mulai isi
                    isi = '<tr>' +
                        '<td class="text-center">' +
                        "<button type='button' class='btn btn-danger btn-sm hapus-item-dokter-lain' title='Hapus'><i class='fa fa-xmark'></i></button>" +
                        "<input type='hidden' class='isi-id-dokter-lain-pendaftaran-pra-operasi' name='dokter_bedah[]' value='" + id_dokter + "'>" +
                        '</td>' +
                        '<td>' +
                        "<div class='form-group'><input type='text' class='form-control isi-dokter-lain-pendaftaran-pra-operasi' value='" + dokter + "' aria-label='Dokter Bedah Lain' readonly></div>" +
                        "<div><textarea class='form-control isi-rencana-tindakan-pendaftaran-pra-operasi' name='rencana_tindakan_operasi[]' aria-label='Rencana Tindakan Operasi untuk Dokter Lain' readonly>" + tindakan + "</textarea></div>" +
                        '</td>' +
                        '</tr>';
                    $(isi).hide().appendTo('#list-dokter-lain-pendaftaran-pra-operasi').fadeIn(1000);
                    // Akhir isi

                    // Mulai bersihkan form`
                    $('#dokter-lain-pendaftaran-pra-operasi').val(0).trigger('change');
                    $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val(null);
                    // Akhri bersihkan form
                    return true;
                } else {
                    return false;
                }
            } else {
                toastr.warning('Sudah ada 5 dokter bedah lain', 'Peringatan', {
                    timeOut: 4000
                });
                return false;
            }
            // Akhir periksa
        }

        // Mulai tambah dokter bedah lain (manual button click)
        $('#tambah-dokter-bedah-lain-pendaftaran-pra-operasi').click(function() {
            if (!tambahDokterBedahLain()) {
                toastr.warning('Mohon isi data dokter bedah lain dan tindakannya lebih dulu', 'Peringatan', {
                    timeOut: 4000
                });
            }
        });
        // Akhir tambah dokter bedah lain

        // Auto-add yang lebih user-friendly - dipicu ketika user pindah ke field lain atau melakukan action
        // HANYA akan auto-add jika KEDUA field sudah terisi (dokter DAN rencana tindakan)
        function checkAutoAdd() {
            let id_dokter = $('#dokter-lain-pendaftaran-pra-operasi').val();
            let tindakan = $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val().trim();
            
            // Validasi ketat: KEDUA field harus terisi
            if (id_dokter !== '' && id_dokter !== null && tindakan !== '' && tindakan.length > 0) {
                tambahDokterBedahLain();
            }
        }

        // Event listener untuk auto-add - hanya pada action tertentu, bukan saat mengetik
        $('#rencana-tindakan-lain-pendaftaran-pra-operasi').on('blur', function() {
            // Auto-add ketika user keluar dari textarea (blur) - tapi hanya jika dokter juga sudah dipilih
            let id_dokter = $('#dokter-lain-pendaftaran-pra-operasi').val();
            if (id_dokter !== '' && id_dokter !== null) {
                checkAutoAdd();
            }
        });

        // Auto-add ketika user mengubah pilihan dokter - tapi hanya jika rencana tindakan juga sudah diisi
        $('#dokter-lain-pendaftaran-pra-operasi').on('change', function() {
            let tindakan = $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val().trim();
            if (tindakan !== '' && tindakan.length > 0) {
                checkAutoAdd();
            }
        });

        // Auto-add ketika user klik field lain setelah mengisi KEDUA field
        $('#diagnosa-medis-pendaftaran-pra-operasi, #lama-operasi-pendaftaran-pra-operasi, #tanggal-pendaftaran-pra-operasi, #waktu-pendaftaran-pra-operasi, #ruangan-operasi-pendaftaran-pra-operasi').on('focus', function() {
            // Auto-add ketika user klik field lain - tapi hanya jika KEDUA field sudah terisi
            checkAutoAdd();
        });

        // Auto-add ketika user klik radio button tujuan operasi, sifat operasi, dll
        $('.tujuanOperasiDafOpe, .sifatOperasiDafOpe, .rencanaJenisPembiusanDafOpe, .potong-beku-pendaftaran-pra-operasi').on('click', function() {
            // Auto-add ketika user klik radio button lain - tapi hanya jika KEDUA field sudah terisi
            checkAutoAdd();
        });

        // Event listener untuk hapus individual dengan icon trash
        $(document).on('click', '.hapus-item-dokter-lain', function() {
            $(this).closest('tr').fadeOut(500, function() {
                $(this).remove();
            });
        });

        // Hapus bulk (tidak digunakan lagi, tapi tetap dipertahankan untuk kompatibilitas)
        $('#hapus-dokter-bedah-lain-pendaftaran-pra-operasi').click(function() {
            $('#list-dokter-lain-pendaftaran-pra-operasi').find($('.pilih-dokter-lain-pendaftaran-pra-operasi')).each(function() {
                if ($(this).is(':checked')) {
                    $(this).parents('tr').fadeOut(500, function() {
                        $(this).remove();
                    });
                }
            });
        });
        // Akhir hapus dokter bedah lain
        //coba
          $('#waktu-pendaftaran-pra-operasi').timepicker({
            showMeridian: false,    
            minuteStep: 1,
            defaultTime: 'current', 
            showInputs: true        
        });
        $('#lama-operasi-pendaftaran-pra-operasi').on('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
        //akhir coba

        // Mulai sifat operasi
        $('.sifatOperasiDafOpe').click(function() {
            let id = $(this).val();
            if (id === '2131') { // CITO
                $('#form-cito-pendaftaran-pra-operasi').removeClass('d-none');
                $('#form-urgent-pendaftaran-pra-operasi, #form-prioritas-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-urgent-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').val(null).trigger('change');
            } else if (id === '6080') { // Urgent
                $('#form-cito-pendaftaran-pra-operasi, #form-prioritas-pendaftaran-pra-operasi').addClass('d-none');
                $('#form-urgent-pendaftaran-pra-operasi').removeClass('d-none');
                $('#alasan-cito-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').val(null).trigger('change');
            } else if (id === '6125') { // Prioritas
                $('#form-cito-pendaftaran-pra-operasi, #form-urgent-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-cito-pendaftaran-pra-operasi, #alasan-urgent-pendaftaran-pra-operasi').val(null).trigger('change');
                $('#form-prioritas-pendaftaran-pra-operasi').removeClass('d-none');
            } else { // Elektif
                $('#form-cito-pendaftaran-pra-operasi, #form-urgent-pendaftaran-pra-operasi, #form-prioritas-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-cito-pendaftaran-pra-operasi, #alasan-urgent-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').val(null).trigger('change');
            }

            // Mulai alasan sifat operasi
            $('#alasan-cito-pendaftaran-pra-operasi, #alasan-urgent-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').select2({
                placeholder: '[ Pilih alasan ]',
                ajax: {
                    url: "<?= base_url('operasi/PengkajianDafOpe/alasan') ?>",
                    dataType: 'json',
                    delay: 250,
                    method: 'POST',
                    data: {
                        sifatOperasi: id,
                        smf: $('#dokter-bedah-pendaftaran-pra-operasi').find(':selected').data('smf')
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            }).val(0).trigger('change');
            // Akhir alasan sifat operasi
        });
        // Akhir sifat operasi

        // Mulai rencana jenis pembiusan
        $('.rencanaJenisPembiusanDafOpe').click(function() {
            let id = $(this).val();
            if (id === '2138') {
                $('#ShowrencanaJenisPembiusan').removeClass('d-none');
            } else {
                $('#ShowrencanaJenisPembiusan').addClass('d-none');
                $('#rencanaJenisPembiusan_lainnya').val(null);
            }
        });

        if ($('.rencanaJenisPembiusanDafOpe:checked').val() === '2138') {
            $('#ShowrencanaJenisPembiusan').removeClass('d-none');
        }
        // Akhir rencana jenis pembiusan

        // Mulai join operasi
        $('.join-operasi-pendaftaran-pra-operasi').click(function() {
            let joinOperasiValue = $(this).val();
            if (joinOperasiValue === '6249') { // Ya
                $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').show();
            } else { // Tidak
                $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').hide();
            }
        });

        // Inisialisasi visibility form dokter bedah lain berdasarkan nilai yang sudah dipilih
        let selectedJoinOperasi = $('.join-operasi-pendaftaran-pra-operasi:checked').val();
        if (selectedJoinOperasi === '6249') {
            $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').show();
        } else {
            $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').hide();
        }
        // Akhir join operasi

        // Mulai fitur cek jumlah operasi untuk ruang operasi ID=16
        function cekJumlahOperasi() {
            let ruangOperasi = $('#ruangan-operasi-pendaftaran-pra-operasi').val();
            let tanggal = $('#tanggal-pendaftaran-pra-operasi').val();
            
            // Hanya tampilkan untuk ruang operasi ID=16 dan tanggal sudah diisi
            if (ruangOperasi === '16' && tanggal !== '') {
                // Tampilkan loading indicator
                $('#text-jumlah-operasi').html('<i class="fa fa-spinner fa-spin"></i> Mengecek jumlah operasi...');
                $('#keterangan-jumlah-operasi').show();
                
                // AJAX call ke controller
                $.ajax({
                    url: "<?= base_url('operasi/PengkajianDafOpe/cekJumlahOperasi') ?>",
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        tanggal: tanggal
                    },
                    success: function(response) {
                        if (response) {
                            $('#text-jumlah-operasi').html('Jumlah Operasi: ' + response.jumlah);
                        } else {
                            $('#text-jumlah-operasi').html('Jumlah Operasi: 0');
                        }
                    },
                    error: function() {
                        $('#text-jumlah-operasi').html('Jumlah Operasi: Error');
                    }
                });
            } else {
                // Sembunyikan keterangan jika bukan ruang operasi ID=16 atau tanggal kosong
                $('#keterangan-jumlah-operasi').hide();
            }
        }

        // Event listener untuk perubahan ruang operasi
        $('#ruangan-operasi-pendaftaran-pra-operasi').on('change', function() {
            cekJumlahOperasi();
        });

        // Event listener untuk perubahan tanggal
        $('#tanggal-pendaftaran-pra-operasi').on('change', function() {
            cekJumlahOperasi();
            cekValidasiTanggalOperasi();
        });
        // Akhir fitur cek jumlah operasi

        // Mulai fitur validasi tanggal operasi
        function cekValidasiTanggalOperasi() {
            let tanggal = $('#tanggal-pendaftaran-pra-operasi').val();
            let norm = "<?= $getNomr['NORM'] ?>";
            let ruang_operasi = $('#ruangan-operasi-pendaftaran-pra-operasi').val();
            
            // Debug log untuk melihat parameter yang akan dikirim
            console.log('cekValidasiTanggalOperasi - norm:', norm, 'tanggal:', tanggal, 'ruang_operasi:', ruang_operasi);
            
            // Hanya lakukan validasi jika ruang operasi ID=16
            if (ruang_operasi === '16' && tanggal !== '' && norm !== '') {
                // AJAX call ke controller untuk cek validasi
                $.ajax({
                    url: "<?= base_url('operasi/PengkajianDafOpe/cekPendaftaranOperasiTanggal') ?>",
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        norm: norm,
                        tanggal: tanggal,
                        ruang_operasi: ruang_operasi
                    },
                    success: function(response) {
                        console.log('Response validasi:', response);
                        if (response.status === 'sudah_ada') {
                            // Format pesan sesuai permintaan: "NORM [norm] di tanggal [tanggal] sudah memiliki perjanjian operasi. Apakah akan dilanjutkan?"
                            let norm = "<?= $getNomr['NORM'] ?>";
                            let tanggalFormat = new Date(tanggal).toLocaleDateString('id-ID', {
                                day: '2-digit',
                                month: '2-digit', 
                                year: 'numeric'
                            });
                            let pesanKonfirmasi = `NORM ${norm} di tanggal ${tanggalFormat} sudah memiliki perjanjian operasi. Apakah akan dilanjutkan?`;
                            
                            // Tampilkan toastr kuning dengan animasi close
                            toastr.warning(pesanKonfirmasi, 'Konfirmasi', {
                                timeOut: 6000,
                                closeButton: true,
                                progressBar: true
                            });
                            
                            // TIDAK reset tanggal - biarkan user tetap bisa melanjutkan
                            // $('#tanggal-pendaftaran-pra-operasi').val(''); // DIHAPUS
                            
                            // Tetap tampilkan keterangan jumlah operasi jika ada
                            // $('#keterangan-jumlah-operasi').hide(); // DIHAPUS
                        } else if (response.status === 'aman') {
                            // Tanggal aman, bisa dilanjutkan
                            console.log('Tanggal aman untuk pendaftaran operasi');
                        } else if (response.status === 'error') {
                            toastr.warning(response.message, 'Peringatan', {
                                timeOut: 4000
                            });
                            console.log('Error detail:', response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        toastr.error('Terjadi kesalahan saat validasi tanggal operasi', 'Error', {
                            timeOut: 6000
                        });
                        console.log('AJAX Error:', xhr.responseText, status, error);
                    }
                });
            } else if (ruang_operasi !== '16') {
                // Jika bukan ruang operasi ID=16, tidak perlu validasi
                console.log('Validasi diabaikan karena bukan ruang operasi IBS C');
            }
        }
        // Akhir fitur validasi tanggal operasi

        // Mulai fitur modal detail operasi dengan server-side processing (TERPISAH)
        let detailOperasiHarianTable = null;
        let detailOperasiRangeTable = null;
        
        // Event listener untuk klik pada teks jumlah operasi (Modal Harian - tanggal spesifik)
        $(document).on('click', '#link-jumlah-operasi', function() {
            let ruangOperasi = $('#ruangan-operasi-pendaftaran-pra-operasi').val();
            let tanggal = $('#tanggal-pendaftaran-pra-operasi').val();
            
            // Hanya tampilkan modal jika ruang operasi ID=16 dan tanggal sudah diisi
            if (ruangOperasi === '16' && tanggal !== '') {
                // Format tanggal untuk ditampilkan di modal
                let tanggalFormat = new Date(tanggal).toLocaleDateString('id-ID', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                $('#tanggal-modal-operasi-harian').text(tanggalFormat);
                
                // Tampilkan modal harian
                $('#modal-detail-operasi-harian').modal('show');
                
                // Inisialisasi DataTables harian
                if (detailOperasiHarianTable) {
                    detailOperasiHarianTable.destroy();
                }
                
                detailOperasiHarianTable = $('#tabel-detail-operasi-harian').DataTable({
                    processing: true,
                    serverSide: true,
                    responsive: true,
                    ajax: {
                        url: "<?= base_url('operasi/PengkajianDafOpe/getDetailOperasi') ?>",
                        type: 'POST',
                        data: function(d) {
                            // Kirim parameter tanggal spesifik saja
                            d.tanggal = tanggal;
                        }
                    },
                    columns: [
                        { 
                            data: 'no',
                            name: 'no',
                            orderable: false,
                            searchable: false,
                            className: 'text-center'
                        },
                        { 
                            data: 'waktu_mulai',
                            name: 'waktu_mulai',
                            className: 'text-center'
                        },
                        { 
                            data: 'waktu_selesai',
                            name: 'waktu_selesai',
                            className: 'text-center'
                        },
                        { 
                            data: 'tindakan',
                            name: 'tindakan'
                        },
                        { 
                            data: 'dokter',
                            name: 'dokter'
                        },
                        { 
                            data: 'ruangan',
                            name: 'ruangan',
                            orderable: false,
                            searchable: false,
                            className: 'text-center'
                        }
                    ],
                    order: [[1, 'asc']], // Sort by waktu_mulai
                    pageLength: 10,
                    lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
                    language: {
                        'sEmptyTable': 'Tidak ada data operasi untuk tanggal ini',
                        'sInfo': 'Menampilkan _START_ sampai _END_ dari _TOTAL_ operasi',
                        'sInfoEmpty': 'Menampilkan 0 sampai 0 dari 0 operasi',
                        'sInfoFiltered': '(Pencarian dari _MAX_ total operasi)',
                        'sInfoPostFix': '',
                        'sInfoThousands': ',',
                        'sLengthMenu': 'Menampilkan _MENU_ operasi per halaman',
                        'sLoadingRecords': 'Harap tunggu...',
                        'sProcessing': 'Sedang memproses...',
                        'sSearch': 'Pencarian:',
                        'sZeroRecords': 'Tidak ada operasi yang sesuai dengan pencarian',
                        'oPaginate': {
                            'sFirst': 'Pertama',
                            'sLast': 'Terakhir',
                            'sNext': 'Selanjutnya',
                            'sPrevious': 'Sebelumnya'
                        }
                    },
                    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                         '<"row"<"col-sm-12"tr>>' +
                         '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                    scrollX: true,
                    autoWidth: false
                });
            }
        });
        
        // Event listener untuk destroy DataTable modal harian
        $('#modal-detail-operasi-harian').on('hidden.bs.modal', function() {
            if (detailOperasiHarianTable) {
                detailOperasiHarianTable.destroy();
                detailOperasiHarianTable = null;
            }
        });

        // Event listener untuk klik ikon kalender (Modal List Perjanjian Operasi - BARU)
        // PERBAIKAN: Gunakan timezone Indonesia (WIB) dan HAPUS batasan 14 hari
        let currentStartDate = new Date(); // Tanggal mulai untuk navigasi

        // Helper function untuk mendapatkan tanggal hari ini dalam timezone Indonesia
        function getTodayIndonesia() {
            const now = new Date();
            // Konversi ke timezone Indonesia (UTC+7)
            const indonesiaTime = new Date(now.getTime() + (7 * 60 * 60 * 1000));
            return new Date(indonesiaTime.getFullYear(), indonesiaTime.getMonth(), indonesiaTime.getDate());
        }

        // Helper functions
        function isSunday(date) {
            return date.getDay() === 0;
        }

        function adjustStartDateSkipSunday() {
            // ATURAN BARU: Minggu SELALU di-skip (tidak ada pengecualian)
            console.log('adjustStartDateSkipSunday - currentStartDate before:', currentStartDate.toDateString(), 'Day:', currentStartDate.getDay());

            // Skip semua Minggu sampai ketemu hari kerja
            while (isSunday(currentStartDate)) {
                currentStartDate.setDate(currentStartDate.getDate() + 1);
                console.log('Skipped Sunday, now:', currentStartDate.toDateString());
            }

            console.log('adjustStartDateSkipSunday - currentStartDate after:', currentStartDate.toDateString(), 'Day:', currentStartDate.getDay());
        }

        $('#btn-calendar-modal').on('click', function() {
            let ruangOperasi = $('#ruangan-operasi-pendaftaran-pra-operasi').val();
            if (ruangOperasi === '16') {
                // PERBAIKAN: Set ke tanggal hari ini dengan timezone Indonesia yang benar
                currentStartDate = getTodayIndonesia();

                console.log('=== MODAL OPEN ===');
                console.log('Today Indonesia:', currentStartDate.toDateString());
                console.log('Day of week:', currentStartDate.getDay(), '(' + ['Minggu','Senin','Selasa','Rabu','Kamis','Jumat','Sabtu'][currentStartDate.getDay()] + ')');

                // ATURAN BARU: Skip Minggu MUTLAK (tidak ada pengecualian)
                adjustStartDateSkipSunday();

                // Tampilkan modal
                $('#modal-list-perjanjian-operasi').modal('show');

                // Load data untuk 3 hari pertama dari tanggal sekarang
                loadListPerjanjianOperasi();
            } else {
                toastr.warning('Fitur kalender hanya tersedia untuk Ruang Operasi IBS Gedung C', 'Peringatan', {
                    timeOut: 4000
                });
            }
        });

        // Fungsi untuk load data perjanjian operasi
        function loadListPerjanjianOperasi() {
            $('#loading-perjanjian').show();
            $('#container-3-hari-perjanjian').hide();

            // Generate 3 hari kerja (skip Minggu)
            let hariKerja = generateTigaHariKerja();

            // Update header bulan/tahun berdasarkan hari kerja yang sebenarnya
            updateHeaderBulanTahun(hariKerja);

            // Update status tombol navigasi
            updateNavigationButtons();

            // Helper function to format date as YYYY-MM-DD in local timezone
            function toLocalYmd(date) {
                const pad = (num) => (num < 10 ? '0' : '') + num;
                return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}`;
            }

            $.ajax({
                url: "<?= base_url('operasi/PengkajianDafOpe/getListPerjanjianOperasi') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    tanggal_mulai: toLocalYmd(currentStartDate),
                    hari_kerja: hariKerja.map(d => toLocalYmd(d))
                },
                success: function(response) {
                    $('#loading-perjanjian').hide();
                    $('#container-3-hari-perjanjian').show();

                    if (response.status === 'success') {
                        // Filter data untuk hanya menampilkan hari kerja (bukan Minggu)
                        let filteredData = response.data.filter(function(hari) {
                            return hari.hari !== 'Minggu';
                        });
                        
                        renderListPerjanjianOperasi(filteredData);
                    } else {
                        toastr.error(response.message || 'Gagal memuat data perjanjian', 'Error');
                        $('#container-3-hari-perjanjian').html('<div class="col-12 text-center"><p class="text-danger">Gagal memuat data</p></div>');
                    }
                },
                error: function() {
                    $('#loading-perjanjian').hide();
                    $('#container-3-hari-perjanjian').show();
                    toastr.error('Terjadi kesalahan saat memuat data', 'Error');
                    $('#container-3-hari-perjanjian').html('<div class="col-12 text-center"><p class="text-danger">Terjadi kesalahan</p></div>');
                }
            });
        }

        // ROMBAK TOTAL: Fungsi untuk generate 3 hari kerja (SKIP MINGGU MUTLAK)
        function generateTigaHariKerja() {
            let hariKerja = [];
            let todayIndonesia = getTodayIndonesia();
            let currentDate = new Date(currentStartDate.getFullYear(), currentStartDate.getMonth(), currentStartDate.getDate());

            console.log('=== generateTigaHariKerja START ===');
            console.log('Today Indonesia:', todayIndonesia.toDateString(), 'Day:', todayIndonesia.getDay());
            console.log('CurrentStartDate:', currentDate.toDateString(), 'Day:', currentDate.getDay());

            // ATURAN BARU YANG JELAS:
            // 1. Minggu SELALU di-skip (tidak ditampilkan)
            // 2. Tampilkan 3 hari kerja berturut-turut (Senin-Sabtu)
            // 3. Mulai dari currentStartDate

            // LOOP sampai mendapat TEPAT 3 hari kerja
            while (hariKerja.length < 3) {
                let dayOfWeek = currentDate.getDay(); // 0=Minggu, 1=Senin, dst

                // Hanya masukkan jika BUKAN Minggu (Senin-Sabtu saja)
                if (dayOfWeek !== 0) {
                    hariKerja.push(new Date(currentDate));
                    console.log('Added working day:', currentDate.toDateString(), 'DayOfWeek:', dayOfWeek, 'DayName:', ['Minggu','Senin','Selasa','Rabu','Kamis','Jumat','Sabtu'][dayOfWeek]);
                } else {
                    console.log('Skipped Sunday:', currentDate.toDateString());
                }

                // Geser ke hari berikutnya
                currentDate.setDate(currentDate.getDate() + 1);
            }

            console.log('Final hariKerja:', hariKerja.map(d => d.toDateString() + ' (' + ['Minggu','Senin','Selasa','Rabu','Kamis','Jumat','Sabtu'][d.getDay()] + ')'));
            console.log('=== generateTigaHariKerja END ===');
            return hariKerja;
        }

        // Fungsi untuk update header bulan/tahun berdasarkan hari kerja yang sebenarnya
        function updateHeaderBulanTahun(hariKerja) {
            if (!hariKerja || hariKerja.length === 0) {
                $('#current-month-year-perjanjian').text('-');
                return;
            }

            // Ambil bulan dan tahun dari hari kerja yang sebenarnya
            let bulanTahunList = hariKerja.map(date => {
                return {
                    bulan: date.toLocaleDateString('id-ID', { month: 'long' }),
                    tahun: date.getFullYear(),
                    key: date.getFullYear() + '-' + date.getMonth()
                };
            });

            // Hapus duplikasi berdasarkan key (tahun-bulan)
            let uniqueBulanTahun = [];
            let seenKeys = new Set();
            bulanTahunList.forEach(item => {
                if (!seenKeys.has(item.key)) {
                    seenKeys.add(item.key);
                    uniqueBulanTahun.push(item);
                }
            });

            let headerText = '';
            if (uniqueBulanTahun.length === 1) {
                // Satu bulan saja
                headerText = `${uniqueBulanTahun[0].bulan} ${uniqueBulanTahun[0].tahun}`;
            } else if (uniqueBulanTahun.every(item => item.tahun === uniqueBulanTahun[0].tahun)) {
                // Beda bulan, sama tahun - format: "Agustus - September 2025"
                let bulanAwal = uniqueBulanTahun[0].bulan;
                let bulanAkhir = uniqueBulanTahun[uniqueBulanTahun.length - 1].bulan;
                headerText = `${bulanAwal} - ${bulanAkhir} ${uniqueBulanTahun[0].tahun}`;
            } else {
                // Beda tahun - format: "Desember 2025 - Januari 2026"
                let itemAwal = uniqueBulanTahun[0];
                let itemAkhir = uniqueBulanTahun[uniqueBulanTahun.length - 1];
                headerText = `${itemAwal.bulan} ${itemAwal.tahun} - ${itemAkhir.bulan} ${itemAkhir.tahun}`;
            }

            $('#current-month-year-perjanjian').text(headerText);
        }

        // Fungsi untuk update status tombol navigasi
        function updateNavigationButtons() {
            // PERBAIKAN: Tombol prev disabled jika sudah di tanggal hari ini (timezone Indonesia)
            let todayIndonesia = getTodayIndonesia();
            todayIndonesia.setHours(0, 0, 0, 0);
            currentStartDate.setHours(0, 0, 0, 0);

            $('#btn-prev-range-perjanjian').prop('disabled', currentStartDate.getTime() <= todayIndonesia.getTime());

            // PERBAIKAN: Hapus batasan 14 hari - tombol next selalu aktif
            $('#btn-next-range-perjanjian').prop('disabled', false);
        }

        // Event listener untuk navigasi prev/next
        $('#btn-prev-range-perjanjian').on('click', function() {
            // Geser mundur 3 hari KERJA (bukan kalender)
            currentStartDate = getNextWorkingDays(currentStartDate, -3);
            loadListPerjanjianOperasi();
        });

        $('#btn-next-range-perjanjian').on('click', function() {
            // Geser maju 3 hari KERJA (bukan kalender)
            currentStartDate = getNextWorkingDays(currentStartDate, 3);
            loadListPerjanjianOperasi();
        });
        
        // Fungsi untuk mendapatkan hari kerja berikutnya/sebelumnya
        function getNextWorkingDays(startDate, workingDaysCount) {
            let newDate = new Date(startDate);
            let count = 0;
            let direction = workingDaysCount > 0 ? 1 : -1;
            let targetCount = Math.abs(workingDaysCount);
            
            while (count < targetCount) {
                newDate.setDate(newDate.getDate() + direction);
                
                // Jika bukan Minggu, hitung sebagai hari kerja
                if (!isSunday(newDate)) {
                    count++;
                }
            }
            
            return newDate;
        }

        // ROMBAK: Fungsi render dengan KONSISTENSI struktur kamar database & SELALU 3 hari kerja
        function renderListPerjanjianOperasi(data) {
            let html = '';
            // Util sanitasi tooltip plain text
            const strip = s => (s || '').replace(/&lt;/g,'<').replace(/&gt;/g,'>').replace(/<[^>]*>/g,'').trim();
            const esc = s => s.replace(/"/g,'&quot;');

            // PASTIKAN selalu ada 3 hari kerja - jika kurang, tambahkan hari kosong
            const hariKerjaTarget = generateTigaHariKerja();
            let dataHariKerja = [];

            // Mapping data response ke hari kerja target
            hariKerjaTarget.forEach(function(tanggalTarget, index) {
                let tanggalStr = tanggalTarget.toISOString().split('T')[0];
                let dataHari = data.find(h => h.tanggal === tanggalStr);
                
                if (!dataHari) {
                    // Buat hari kosong jika tidak ada data
                    dataHari = {
                        tanggal: tanggalStr,
                        hari: tanggalTarget.toLocaleDateString('id-ID', { weekday: 'long' }),
                        kamar_list: getDefaultKamarList(), // Ambil dari db_master.tb_kamar
                        slots_by_kamar: [],
                        total_kamar: 2,
                        slot_per_kamar: 4,
                        total_slot: 8
                    };
                }
                dataHariKerja.push(dataHari);
            });

            // Render TEPAT 3 hari kerja
            dataHariKerja.forEach(function(hari) {
                let tanggalObj = new Date(hari.tanggal);
                let namaHari = hari.hari;
                let tanggalFormat = tanggalObj.getDate();

                html += `
                    <div class="col-md-4">
                        <div class="hari-container">
                            <div class="hari-header">
                                <div class="hari-nama">${namaHari}</div>
                                <div class="hari-tanggal">${tanggalFormat}</div>
                            </div>
                `;

                // SELALU gunakan struktur kamar dari database - TIDAK ADA FALLBACK
                let kamarList = hari.kamar_list || getDefaultKamarList();
                let slotPerKamar = 4; // Fixed 4 slot per kamar

                kamarList.forEach(function(kamar, kamarIndex) {
                    // Hitung jumlah slot untuk kamar ini (dinamis berdasarkan data)
                    let maxSlotKamar = hari.max_slots_per_kamar && hari.max_slots_per_kamar[kamar.id]
                                      ? hari.max_slots_per_kamar[kamar.id]
                                      : 4; // Default 4 slot

                    html += `
                        <div class="kamar-section">
                            <div class="kamar-header">
                                <strong>${kamar.nama}</strong>
                                <button type="button" class="btn-tambah-kuota" onclick="tambahKuotaKamar('${kamar.id}', ${kamarIndex}, '${hari.tanggal}')" title="Tambah Kuota Operasi">
                                    <i class="fa fa-plus-circle"></i>
                                </button>
                            </div>
                            <div class="slot-grid-kamar" id="slot-grid-${kamar.id}-${hari.tanggal}">
                    `;

                    // Render slot dinamis per kamar
                    for (let slotIndex = 0; slotIndex < maxSlotKamar; slotIndex++) {
                        let slot = hari.slots_by_kamar && hari.slots_by_kamar[kamarIndex] 
                                  ? hari.slots_by_kamar[kamarIndex][slotIndex] 
                                  : null;

                        if (slot) {
                            // Slot terisi - tentukan class dan behavior berdasarkan status_jadwal
                            let statusClass = '';
                            let statusJadwal = slot.status_jadwal || null;
                            let onClickHandler = '';
                            let cursorStyle = '';
                            let tooltipData = '';
                            
                            // Cek apakah slot memiliki id_penjadwalan (terjadwal penuh)
                            if (slot.id_penjadwalan != null) {
                                // Slot terjadwal (hijau, non-klik; tooltip tetap aktif)
                                statusClass = 'slot-terjadwal';
                                onClickHandler = '';
                                cursorStyle = 'cursor: not-allowed;';
                                const tooltipText = `Pasien: ${strip(slot.pasien)} | Dokter: ${strip(slot.dokter)} | Tindakan: ${strip(slot.tindakan)} | Waktu: ${slot.waktu}`;
                                tooltipData = `title="${esc(tooltipText)}" data-toggle="tooltip"`;
                            } else {
                                // Slot perjanjian (kuning, bisa diklik)
                                statusClass = 'slot-perjanjian';
                                onClickHandler = `onclick="pilihSlotTerisi('${hari.tanggal}', '${slot.waktu}', '${statusJadwal || ''}', '${kamar.id}', '${kamar.nama}', ${slotIndex + 1}, event)"`;
                                cursorStyle = 'cursor: pointer;';
                                const tooltipText = `Pasien: ${strip(slot.pasien)} | Dokter: ${strip(slot.dokter)} | Tindakan: ${strip(slot.tindakan)} | Waktu: ${slot.waktu} | Klik untuk gunakan slot`;
                                tooltipData = `title="${esc(tooltipText)}" data-toggle="tooltip"`;
                            }

                            html += `
                                <div class="slot-operasi ${statusClass}"
                                     data-tanggal="${hari.tanggal}"
                                     data-slot="${slotIndex + 1}"
                                     data-kamar-id="${kamar.id}"
                                     data-kamar-nama="${kamar.nama}"
                                     data-waktu-selesai="${slot.waktu_selesai_plus_interval}"
                                     data-status-jadwal="${statusJadwal || ''}"
                                     data-durasi="${slot.durasi_final || 60}"
                                     data-jam-mulai="${slot.jam_mulai || ''}"
                                     data-jam-selesai="${slot.waktu_selesai || ''}"
                                     style="${cursorStyle}"
                                     ${tooltipData}
                                     ${onClickHandler}>
                                    <div class="slot-waktu">${slot.waktu}</div>
                                    <div class="slot-info">
                                        <div class="slot-info-item">
                                            <span class="slot-info-label">Pasien:</span>
                                            <span class="slot-info-value">${slot.pasien}</span>
                                        </div>
                                        <div class="slot-info-item">
                                            <span class="slot-info-label">Dokter:</span>
                                            <span class="slot-info-value">${slot.dokter}</span>
                                        </div>
                                        <div class="slot-info-item">
                                            <span class="slot-info-label">Tindakan:</span>
                                            <span class="slot-info-value">${slot.tindakan}</span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        } else {
                            // Slot kosong
                            html += `
                                <div class="slot-operasi kosong"
                                     data-tanggal="${hari.tanggal}"
                                     data-slot="${slotIndex + 1}"
                                     data-kamar-id="${kamar.id}"
                                     data-kamar-nama="${kamar.nama}"
                                     onclick="pilihSlotKosong('${hari.tanggal}', ${slotIndex + 1}, '${kamar.id}', '${kamar.nama}', event)">
                                    <div class="slot-info">
                                        <div class="slot-kosong-icon">
                                            <i class="fa fa-plus"></i>
                                        </div>
                                        <div class="slot-kosong-text">Kuota ${slotIndex + 1}</div>
                                    </div>
                                </div>
                            `;
                        }
                    }

                    html += `
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            });

            $('#container-3-hari-perjanjian').html(html);
            
            // Re-init tooltip aman (plain text) + konversi multiline
            (function(){
                if(!document.getElementById('tooltipMultilineStyle')){
                    var st=document.createElement('style');
                    st.id='tooltipMultilineStyle';
                    st.innerHTML='.tooltip-inner{white-space:pre-line;font-size:14px;max-width:300px;} @media(max-width:768px){.tooltip-inner{font-size:12px;max-width:250px;}} @media(max-width:480px){.tooltip-inner{font-size:11px;max-width:200px;}}';
                    document.head.appendChild(st);
                }
                $('[data-toggle="tooltip"]').each(function(){
                    var $el=$(this);
                    var t=$el.attr('title')||$el.attr('data-original-title');
                    if(t && t.indexOf(' | ')!==-1 && t.indexOf('\n')===-1){
                        var multi=t.replace(/ \| /g,'\n');
                        $el.attr('title',multi).attr('data-original-title',multi);
                    }
                });
            })();
            initTooltips();
            // Cegah klik pada slot terjadwal (tooltip tetap muncul)
            $(document).off('click.slotTerjadwal').on('click.slotTerjadwal', '.slot-operasi.slot-terjadwal', function(e){
                e.preventDefault();
                e.stopImmediatePropagation();
                return false;
            });
        }

        // Fungsi untuk mendapatkan struktur kamar default dari db_master.tb_kamar
        function getDefaultKamarList() {
            return [
                { id: '1', nama: 'OKA 1 Gedung C' },
                { id: '2', nama: 'OKA 2 Gedung C' }
            ];
        }



        // Akhir fitur modal List Perjanjian Operasi

        // Fungsi global untuk pilih slot kosong (dipanggil dari onclick)
        window.pilihSlotKosong = function(tanggal, slotNumber, kamarId, kamarNama, ev) {
            if (ev && ev.stopPropagation) {
                ev.stopPropagation();
            }

            let norm = "<?= $getNomr['NORM'] ?>";
            
            // Validasi: cek apakah pasien sudah punya perjanjian di tanggal tersebut
            $.ajax({
                url: "<?= base_url('operasi/PengkajianDafOpe/cekPendaftaranOperasiTanggal') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    norm: norm,
                    tanggal: tanggal,
                    ruang_operasi: '16' // Ruang operasi IBS Gedung C
                },
                success: function(response) {
                    if (response.status === 'sudah_ada') {
                        // Format tanggal untuk ditampilkan
                        let tanggalFormat = new Date(tanggal).toLocaleDateString('id-ID', {
                            day: '2-digit',
                            month: '2-digit', 
                            year: 'numeric'
                        });
                        
                        // SweetAlert konfirmasi: NORM ini sudah memiliki perjanjian operasi di hari [tanggal]
                        swal({
                            title: 'Pasien Sudah Ada Perjanjian',
                            text: `NORM ${norm} sudah memiliki perjanjian operasi di hari ${tanggalFormat}. Apakah akan dilanjutkan?`,
                            type: 'warning',
                            showCancelButton: true,
                            confirmButtonText: 'Ya, Lanjutkan',
                            cancelButtonText: 'Tidak, Batal'
                        }, function(isConfirm) {
                            if (isConfirm) {
                                // User pilih "Ya" - tutup modal dan set data
                                prosesSlotKosongTerpilih(tanggal, slotNumber, kamarId, kamarNama);
                            } else {
                                // User pilih "Tidak" - tetap di modal
                                swal('Pilih Kuota Lain', 'Silakan pilih kuota lain atau tanggal yang berbeda.', 'info');
                            }
                        });
                    } else {
                        // Pasien belum ada perjanjian di tanggal ini - lanjutkan normal
                        prosesSlotKosongTerpilih(tanggal, slotNumber, kamarId, kamarNama);
                    }
                },
                error: function() {
                    // Jika error, lanjutkan normal (fail-safe)
                    prosesSlotKosongTerpilih(tanggal, slotNumber, kamarId, kamarNama);
                }
            });
        };

        // Fungsi untuk memproses slot kosong yang sudah dikonfirmasi
        function prosesSlotKosongTerpilih(tanggal, slotNumber, kamarId, kamarNama) {
            // Cari data operasi yang sudah ada untuk tanggal dan kamar yang dipilih
            const waktuRekomendasi = hitungWaktuRekomendasi(tanggal, kamarId);

            // Set nilai ke form utama
            $('#tanggal-pendaftaran-pra-operasi').val(tanggal).trigger('change');
            $('#waktu-pendaftaran-pra-operasi').val(waktuRekomendasi);
            $('#slot-operasi-pendaftaran-pra-operasi').val(slotNumber);
            $('#id-kamar-pendaftaran-pra-operasi').val(kamarId); // SET ID KAMAR

            // Tutup modal
            $('#modal-list-perjanjian-operasi').modal('hide');

            // Tampilkan notifikasi
            const tanggalObj = new Date(tanggal);
            const tanggalFormat = tanggalObj.toLocaleDateString('id-ID', {
                weekday: 'long',
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });

            toastr.success(`Kuota ${slotNumber} dipilih: ${tanggalFormat} pada ${waktuRekomendasi} (Rekomendasi untuk ${kamarNama})`, 'Kuota Terpilih', {
                timeOut: 5000
            });
        }

        // Fungsi helper untuk convert waktu ke menit
        function timeToMinutes(timeStr) {
            if (!timeStr || typeof timeStr !== 'string') return 0;
            
            try {
                const [jam, menit] = timeStr.split(':').map(Number);
                return jam * 60 + menit;
            } catch (error) {
                console.warn('Error parsing waktu:', timeStr, error);
                return 0; // Fallback
            }
        }

        // ROMBAK TOTAL: Fungsi untuk menghitung waktu rekomendasi berdasarkan waktu_selesai_plus_interval
        function hitungWaktuRekomendasi(tanggal, kamarId) {
            const waktuMulaiDefault = '08:30';
            const batasWaktuMalam = '22:30';

            // Cari container untuk tanggal yang dipilih
            const containerHari = $('#container-3-hari-perjanjian').find(`.hari-container:has([data-tanggal="${tanggal}"])`);
            if (!containerHari.length) {
                return waktuMulaiDefault;
            }

            // Kumpulkan semua slot terisi untuk kamar spesifik
            let jadwalOperasi = [];
            let waktuRekomendasiTerisi = [];

            containerHari.find(`.slot-operasi.terisi[data-kamar-id="${kamarId}"], .slot-operasi.slot-perjanjian[data-kamar-id="${kamarId}"], .slot-operasi.slot-terjadwal[data-kamar-id="${kamarId}"]`).each(function() {
                let jamMulai = $(this).data('jam-mulai');
                let jamSelesai = $(this).data('waktu-selesai'); // waktu_selesai_plus_interval
                let jenis = $(this).hasClass('slot-perjanjian') ? 'perjanjian' : 'penjadwalan';

                if (jamMulai && jamSelesai) {
                    jadwalOperasi.push({ start: jamMulai, end: jamSelesai, jenis: jenis });

                    // Untuk slot terisi yang masih perjanjian, ambil jam_operasi sebagai rekomendasi
                    if (jenis === 'perjanjian') {
                        waktuRekomendasiTerisi.push(jamMulai);
                    }
                }
            });

            // Handle jika kamar kosong
            if (jadwalOperasi.length === 0) {
                return waktuMulaiDefault;
            }

            // Jika ada slot terisi yang masih perjanjian, ambil waktu rekomendasinya
            if (waktuRekomendasiTerisi.length > 0) {
                // Ambil waktu paling awal dari slot perjanjian
                waktuRekomendasiTerisi.sort();
                return waktuRekomendasiTerisi[0];
            }

            // Untuk slot kosong, ambil waktu_selesai_plus_interval paling awal
            let waktuSelesaiList = jadwalOperasi.map(j => j.end).sort();
            if (waktuSelesaiList.length > 0) {
                // Ambil waktu selesai paling awal (slot pertama selesai)
                return waktuSelesaiList[0];
            }

            // Peringatan jika slot sudah penuh (non-blocking)
            if (jadwalOperasi.length >= 4) {
                toastr.warning(`Kamar ini sudah memiliki ${jadwalOperasi.length} jadwal. Harap periksa kembali.`, 'Kuota Penuh', { timeOut: 5000 });
            }

            // Sortir jadwal berdasarkan waktu mulai
            jadwalOperasi.sort((a, b) => timeToMinutes(a.start) - timeToMinutes(b.start));

            // Logika "Cari Celah Paling Pagi"
            let waktuRekomendasi = waktuMulaiDefault;

            for (const jadwal of jadwalOperasi) {
                // Jika waktu rekomendasi saat ini berada sebelum jadwal berikutnya, berarti ada celah.
                if (timeToMinutes(waktuRekomendasi) < timeToMinutes(jadwal.start)) {
                    // Celah ditemukan, waktu rekomendasi saat ini adalah yang paling pagi.
                    break; 
                } else {
                    // Tidak ada celah, geser waktu rekomendasi ke akhir jadwal saat ini.
                    waktuRekomendasi = jadwal.end;
                }
            }

            // Cek apakah waktu rekomendasi melebihi batas malam
            if (timeToMinutes(waktuRekomendasi) > timeToMinutes(batasWaktuMalam)) {
                toastr.warning(`Waktu rekomendasi (${waktuRekomendasi}) telah melewati batas maksimal (${batasWaktuMalam}).`, 'Batas Waktu Terlampaui', { timeOut: 5000 });
            }

            return waktuRekomendasi;
        }

        // Fungsi global untuk pilih slot terisi (dipanggil dari onclick)
        window.pilihSlotTerisi = function(tanggal, waktu, statusJadwal, kamarId, kamarNama, slotNumber, ev) {
            if (ev && ev.stopPropagation) {
                ev.stopPropagation();
            }

            // Format tanggal untuk ditampilkan
            let tanggalObj = new Date(tanggal);
            let tanggalFormat = tanggalObj.toLocaleDateString('id-ID', {
                day: '2-digit',
                month: '2-digit', 
                year: 'numeric'
            });

            // Berikan respons berbeda berdasarkan status jadwal
            if (statusJadwal == 5) {
                // Status = 5 (selesai): SweetAlert konfirmasi
                swal({
                    title: 'Sesi Sudah Selesai',
                    text: `Sesi ini sudah selesai dioperasi. Apakah Anda yakin akan membuat perjanjian di tanggal ${tanggalFormat}?`,
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Lanjutkan',
                    cancelButtonText: 'Tidak, Batal'
                }, function(isConfirm) {
                    if (isConfirm) {
                        // User pilih "Ya" - proses seperti slot kosong
                        prosesSlotSelesai(tanggal, waktu, kamarId, kamarNama, slotNumber);
                    } else {
                        // User pilih "Tidak" - tetap di modal
                        swal('Pilih Kuota Lain', 'Silakan pilih kuota kosong lainnya untuk membuat perjanjian operasi.', 'info');
                    }
                });
            } else if (statusJadwal != 0 && statusJadwal != 5 && statusJadwal != '') {
                // Status bukan 5 atau 0: Toastr warning
                toastr.warning('Sesi ini sudah terjadwalkan, pilih sesi yang kosong', 'Peringatan', {
                    timeOut: 4000,
                    closeButton: true,
                    progressBar: true
                });
            } else {
                // Status kosong atau 0: perilaku default (konfirmasi)
                swal({
                    title: 'Konfirmasi Ruang Terisi',
                    text: `Slot ini sudah terisi pada ${waktu}. Apakah Anda ingin tetap menggunakan tanggal ini dengan waktu rekomendasi yang tersedia?`,
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Lanjutkan',
                    cancelButtonText: 'Tidak, Batal'
                }, function(isConfirm) {
                    if (isConfirm) {
                        // User pilih "Ya" - lanjutkan dengan logic seperti pilihSlotKosong
                        prosesSlotTerisi(tanggal, waktu, kamarId, kamarNama, slotNumber);
                    } else {
                        // User pilih "Tidak" - tetap di modal, tampilkan info ringan
                        swal('Pilih Kuota Lain', 'Silakan pilih kuota kosong lainnya untuk membuat perjanjian operasi.', 'info');
                    }
                });
            }
        };

        // Fungsi untuk memproses slot terisi yang dikonfirmasi user
        function prosesSlotTerisi(tanggal, waktu, kamarId, kamarNama, slotNumber) {
            // Hitung waktu rekomendasi berdasarkan operasi yang sudah ada
            const waktuRekomendasi = hitungWaktuRekomendasi(tanggal, kamarId);

            // Set nilai ke form utama
            $('#tanggal-pendaftaran-pra-operasi').val(tanggal).trigger('change');
            $('#waktu-pendaftaran-pra-operasi').val(waktuRekomendasi);
            $('#slot-operasi-pendaftaran-pra-operasi').val(slotNumber);
            $('#id-kamar-pendaftaran-pra-operasi').val(kamarId); // SET ID KAMAR

            // Tutup modal
            $('#modal-list-perjanjian-operasi').modal('hide');

            // Tampilkan notifikasi sukses
            const tanggalObj = new Date(tanggal);
            const tanggalFormat = tanggalObj.toLocaleDateString('id-ID', {
                weekday: 'long',
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });

            swal({
                title: 'Kuota Terpilih',
                text: `Tanggal: ${tanggalFormat}\nWaktu rekomendasi: ${waktuRekomendasi}\nRuang: ${kamarNama}`,
                type: 'success',
                confirmButtonText: 'OK',
                timer: 5000
            });
        }

        // Fungsi untuk memproses slot yang sudah selesai (status = 5)
        function prosesSlotSelesai(tanggal, waktu, kamarId, kamarNama, slotNumber) {
            // Hitung waktu rekomendasi berdasarkan operasi yang sudah ada
            const waktuRekomendasi = hitungWaktuRekomendasi(tanggal, kamarId);

            // Set nilai ke form utama
            $('#tanggal-pendaftaran-pra-operasi').val(tanggal).trigger('change');
            $('#waktu-pendaftaran-pra-operasi').val(waktuRekomendasi);
            $('#slot-operasi-pendaftaran-pra-operasi').val(slotNumber);
            $('#id-kamar-pendaftaran-pra-operasi').val(kamarId); // SET ID KAMAR

            // Tutup modal
            $('#modal-list-perjanjian-operasi').modal('hide');

            // Tampilkan notifikasi sukses
            const tanggalObj = new Date(tanggal);
            const tanggalFormat = tanggalObj.toLocaleDateString('id-ID', {
                weekday: 'long',
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });

            swal({
                title: 'Kuota Terpilih',
                text: `Tanggal: ${tanggalFormat}\nWaktu rekomendasi: ${waktuRekomendasi}\nRuang: ${kamarNama}`,
                type: 'success',
                confirmButtonText: 'OK',
                timer: 5000
            });
        }

        // Mulai simpan daftar operasi dengan validasi
        $('#form-pendaftaran-pra-operasi').submit(function(event) {
            event.preventDefault();
            
            // Validasi join operasi
            let joinOperasiValue = $('.join-operasi-pendaftaran-pra-operasi:checked').val();
            let jumlahDokterLain = $('.hapus-item-dokter-lain').length;
            
            // Jika pilih "Ya" tapi list dokter bedah lain kosong
            if (joinOperasiValue === '6249' && jumlahDokterLain === 0) {
                toastr.warning('Dokter Bedah Lain dan Tindakannya harus diisi karena Anda memilih "Ya" pada Join Operasi', 'Peringatan', {
                    timeOut: 4000
                });
                return false;
            }
            if (!joinOperasiValue) {
                toastr.warning('Silakan pilih opsi Join Operasi (Ya/Tidak)', 'Peringatan', {
                    timeOut: 4000
                });
                return false;
            }
            
            let dataDAF_OPERASI = $(this).serializeArray();

            $.ajax({
                dataType: 'json',
                url: "<?= base_url('operasi/PengkajianDafOpe/action_dafoperasi/tambah') ?>",
                method: 'POST',
                data: dataDAF_OPERASI,
                success: function(data) {
                    if (data.status === 'success') {
                        toastr.success('Data tersimpan', 'Berhasil', {
                            timeOut: 3000
                        });
                        location.reload();
                    } else {
                        $.each(data.errors, function(index, element) {
                            toastr.warning(element, 'Peringatan', {
                                timeOut: 4000
                            });
                        });
                    }
                }
            });
        });
        // Akhir simpan daftar operasi

        // Mulai tampil daftar pra operasi
        $('#ubah-pendaftaran-pra-operasi').on('show.bs.modal', function(e) {
            let id = $(e.relatedTarget).data('id');
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/PengkajianDafOpe/viewDaftarPraOperasi') ?>",
                data: {
                    id: id
                },
                success: function(data) {
                    $('#hasilDaftarPraOperasi').html(data);
                }
            });
            // e.preventDefault();
        });
        // Akhir tampil daftar pra operasi

        // Mulai lengkapi reservasi
        // Variable untuk tracking nilai awal select2
        let initialCaraBayar = null;
        let initialKelasRawat = null;
        let initialTglRencanaMasuk = null;
        
        // Inisialisasi select2 untuk cara bayar dan kelas rawat dengan data dari backend
        $('#reservasi-cara-bayar').select2({
            placeholder: '[ Pilih Cara Bayar ]',
            dropdownParent: $('#modal-lengkapi-reservasi'),
            // allowClear: true
        });

        $('#reservasi-kelas-rawat').select2({
            placeholder: '[ Pilih Kelas Rawat ]',
            dropdownParent: $('#modal-lengkapi-reservasi'),
            // allowClear: true
        });
        
        // Event listener untuk modal lengkapi reservasi
        $('#modal-lengkapi-reservasi').on('show.bs.modal', function(e) {
            let id = $(e.relatedTarget).data('id');
            
            // Reset form dan initial values
            $('#form-lengkapi-reservasi')[0].reset();
            initialCaraBayar = null;
            initialKelasRawat = null;
            initialTglRencanaMasuk = null;
            
            // Load data reservasi
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/PengkajianDafOpe/getDataReservasi') ?>",
                data: { id: id },
                dataType: 'json',
                success: function(data) {
                    if (data.status === 'success') {
                        let res = data.data;
                        
                        // Isi field readonly
                        $('#reservasi-id-pendaftaran').val(res.id_pendaftaran);
                        $('#reservasi-id-reservasi').val(res.id_reservasi);
                        $('#reservasi-norm-nama').val(res.norm + ' [' + res.nama_pasien + ']');
                        $('#reservasi-dokter').val(res.nama_dokter);
                        $('#reservasi-kode-booking').val(res.kode_booking);
                        $('#reservasi-tanggal-operasi').val(res.tanggal_operasi);
                        $('#reservasi-diagnosis').val(res.diagnosis);
                        $('#reservasi-tindakan').val(res.tindakan);
                        
                        // Set nilai awal untuk tracking
                        initialCaraBayar = res.id_cara_bayar || null;
                        initialKelasRawat = res.id_kelas || null;
                        initialTglRencanaMasuk = res.tgl_rencanaMasuk || null;
                        
                        // Set selected value langsung tanpa AJAX - data sudah tersedia di backend
                        if (res.id_cara_bayar) {
                            $('#reservasi-cara-bayar').val(res.id_cara_bayar).trigger('change');
                        }
                        if (res.id_kelas) {
                            $('#reservasi-kelas-rawat').val(res.id_kelas).trigger('change');
                        }
                        if (res.tgl_rencanaMasuk) {
                            $('#reservasi-tgl-rencana-masuk').val(res.tgl_rencanaMasuk);
                        }
                        if (res.tanggal_operasi) {
                            $('#reservasi-tanggal-operasi-update').val(res.tanggal_operasi);
                        }
                    } else {
                        toastr.error(data.message || 'Gagal memuat data reservasi', 'Error');
                    }
                },
                error: function() {
                    toastr.error('Terjadi kesalahan saat memuat data reservasi', 'Error');
                }
            });
        });

        // Auto-update saat select berubah (dengan validasi perubahan)
        $('#reservasi-cara-bayar').on('change', function() {
            let currentValue = $(this).val() || null;
            
            // Hanya update jika value berubah dari nilai awal
            if (currentValue !== initialCaraBayar) {
                updateReservasi('id_cara_bayar', currentValue);
                initialCaraBayar = currentValue; // Update nilai tracking
            }
        });

        $('#reservasi-kelas-rawat').on('change', function() {
            let currentValue = $(this).val() || null;
            
            // Hanya update jika value berubah dari nilai awal
            if (currentValue !== initialKelasRawat) {
                updateReservasi('id_kelas', currentValue);
                initialKelasRawat = currentValue; // Update nilai tracking
            }
        });

        $('#reservasi-tgl-rencana-masuk').on('change', function() {
            let currentValue = $(this).val() || null;
            
            // Hanya update jika value berubah dari nilai awal
            if (currentValue !== initialTglRencanaMasuk) {
                updateReservasiTanggal('tgl_rencanaMasuk', currentValue);
                initialTglRencanaMasuk = currentValue; // Update nilai tracking
            }
        });

        // Auto-update untuk tanggal operasi update
        $('#reservasi-tanggal-operasi-update').on('change', function() {
            let currentValue = $(this).val() || null;
            let id_pendaftaran = $('#reservasi-id-pendaftaran').val();
            
            if (currentValue && id_pendaftaran) {
                updateTanggalOperasi(id_pendaftaran, currentValue);
            }
        });

        // Fungsi untuk update reservasi (dengan validasi tambahan)
        function updateReservasi(field, value) {
            let id_reservasi = $('#reservasi-id-reservasi').val();
            
            if (!id_reservasi) {
                toastr.warning('ID Reservasi tidak ditemukan', 'Peringatan');
                return;
            }
            
            // Tampilkan loading indicator
            let fieldLabel = '';
            if (field === 'id_cara_bayar') {
                fieldLabel = 'Cara Bayar';
            } else if (field === 'id_kelas') {
                fieldLabel = 'Kelas Rawat';
            }
            
            toastr.info('Menyimpan ' + fieldLabel + '...', 'Proses', {
                timeOut: 1000
            });
            
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/PengkajianDafOpe/updateReservasi') ?>",
                data: {
                    id_reservasi: id_reservasi,
                    field: field,
                    value: value
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        toastr.success(response.message, 'Berhasil', {
                            timeOut: 2000
                        });
                    } else {
                        toastr.error(response.message || 'Gagal update data reservasi', 'Error');
                    }
                },
                error: function() {
                    toastr.error('Terjadi kesalahan saat update data reservasi', 'Error');
                }
            });
        }

        // Fungsi untuk update tanggal reservasi (tgl_rencanaMasuk -> TANGGALRAWATINAP)
        function updateReservasiTanggal(field, value) {
            let id_reservasi = $('#reservasi-id-reservasi').val();
            let id_pendaftaran = $('#reservasi-id-pendaftaran').val();
            
            if (!id_reservasi) {
                toastr.warning('ID Reservasi tidak ditemukan', 'Peringatan');
                return;
            }
            if (!id_pendaftaran) {
                toastr.warning('ID Pendaftaran tidak ditemukan', 'Peringatan');
                return;
            }
            
            toastr.info('Menyimpan Tanggal Rencana Masuk...', 'Proses', {
                timeOut: 1000
            });
            
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/PengkajianDafOpe/updateReservasiTanggal') ?>",
                data: {
                    id_reservasi: id_reservasi,
                    id_pendaftaran: id_pendaftaran,
                    field: field,
                    value: value
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        toastr.success(response.message, 'Berhasil', {
                            timeOut: 2000
                        });
                    } else {
                        toastr.error(response.message || 'Gagal update tanggal rencana masuk', 'Error');
                    }
                },
                error: function() {
                    toastr.error('Terjadi kesalahan saat update tanggal rencana masuk', 'Error');
                }
            });
        }

        // Fungsi untuk update tanggal operasi (ke 2 tabel)
        function updateTanggalOperasi(id_pendaftaran, tanggal) {
            if (!id_pendaftaran || !tanggal) {
                toastr.warning('Data tidak lengkap untuk update tanggal operasi', 'Peringatan');
                return;
            }
            
            toastr.info('Menyimpan Tanggal Operasi...', 'Proses', {
                timeOut: 1000
            });
            
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/PengkajianDafOpe/updateTanggalOperasi') ?>",
                data: {
                    id_pendaftaran: id_pendaftaran,
                    tanggal_operasi: tanggal
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        toastr.success(response.message, 'Berhasil', {
                            timeOut: 2000
                        });
                        
                        // Update field readonly juga
                        $('#reservasi-tanggal-operasi').val(tanggal);
                    } else {
                        toastr.error(response.message || 'Gagal update tanggal operasi', 'Error');
                    }
                },
                error: function() {
                    toastr.error('Terjadi kesalahan saat update tanggal operasi', 'Error');
                }
            });
        }
        // Akhir lengkapi reservasi

        <?php if (isset($getNomr['GEDUNG']) && $getNomr['GEDUNG'] == 1): ?>
        let tabelPerjanjianOperasi = null;
        let isTabelPerjanjianInitialized = false;

        $('a[href="#tab-perjanjian-operasi"]').on('shown.bs.tab', function (e) {
            if (!isTabelPerjanjianInitialized) {
                isTabelPerjanjianInitialized = true;

                // Atur filter default saat tab pertama kali dibuka
                $('#filter-status-group .btn-filter-perjanjian, #filter-status-group .btn-filter-penjadwalan').addClass('active');

                tabelPerjanjianOperasi = $('#tabel-perjanjian-operasi').DataTable({
                    processing: true,
                    serverSide: true,
                    responsive: true,
                    ajax: {
                        url: "<?= base_url('operasi/PengkajianDafOpe/getDaftarPerjanjianOperasi') ?>",
                        type: 'POST',
                        data: function(d) {
                            d.tanggal_dari = $('#filter-tanggal-dari').val();
                            d.tanggal_sampai = $('#filter-tanggal-sampai').val();
                            d.status_filter = getActiveStatusFilter();
                            d.me_filter = $('#filter-status-group .btn-filter-me').hasClass('active');
                        }
                    },
                    columns: [
                        { data: 'no', name: 'no', orderable: false, searchable: false, className: 'text-center' },
                        { data: 'tanggal_operasi', name: 'tanggal_operasi', className: 'text-center' },
                        { data: 'nama_pasien', name: 'nama_pasien' },
                        { data: 'nama_dokter', name: 'nama_dokter' },
                        { 
                            data: 'jam_operasi', 
                            name: 'jam_operasi', 
                            className: 'text-center', 
                            orderable: false,
                            render: function(data, type, row) {
                                let color = (row.status == 'Penjadwalan') ? '#28A745' : '#FFC107';
                                return `<span class="badge" style="background-color: white; border: 2px solid ${color}; color: ${color}; border-radius: 15px; padding: 5px 10px;">${data}</span>`;
                            }
                        },
                        { 
                            data: 'status', 
                            name: 'status', 
                            className: 'text-center', 
                            orderable: false,
                            render: function(data, type, row) {
                                let bgColor = (data == 'Penjadwalan') ? '#28A745' : '#FFC107';
                                return `<span class="badge" style="background-color: ${bgColor}; color: white; border-radius: 15px; padding: 5px 10px;">${data}</span>`;
                            }
                        }
                    ],
                    columnDefs: [
                        { targets: [1, 4], searchable: true }
                    ],
                    order: [[1, 'asc']],
                    language: {
                        "sEmptyTable": "Maaf, tidak ada data yang tersedia. Silakan pilih filter di atas.",
                        "sInfo": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                        "sInfoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
                        "sInfoFiltered": "(Pencarian dari _MAX_ total data)",
                        "sLengthMenu": "Menampilkan _MENU_ data",
                        "sProcessing": "Sedang memproses...",
                        "sSearch": "Pencarian:",
                        "sZeroRecords": "Data tidak ditemukan",
                        "oPaginate": { "sFirst": "Pertama", "sLast": "Terakhir", "sNext": "Selanjutnya", "sPrevious": "Sebelumnya" }
                    },
                    createdRow: function(row, data, dataIndex) {
                        if (data.DT_RowClass) {
                            $(row).addClass(data.DT_RowClass);
                        }
                    }
                });
            }
            tabelPerjanjianOperasi.on('xhr.dt', function(e, settings, json, xhr) {
    console.log("Response JSON:", json);
});

        });

        // Event handlers untuk filter
        $('#filter-tanggal-dari, #filter-tanggal-sampai').on('change', function() {
            if (tabelPerjanjianOperasi) {
                tabelPerjanjianOperasi.ajax.reload();
            }
        });

        $('#filter-status-group .btn').on('click', function() {
            $(this).toggleClass('active');
            if (tabelPerjanjianOperasi) {
                tabelPerjanjianOperasi.ajax.reload();
            }
        });

        function getActiveStatusFilter() {
            let activeFilters = [];
            if ($('#filter-status-group .btn-filter-perjanjian').hasClass('active')) {
                activeFilters.push('perjanjian');
            }
            if ($('#filter-status-group .btn-filter-penjadwalan').hasClass('active')) {
                activeFilters.push('penjadwalan');
            }

            // Jika tidak ada yang aktif atau keduanya aktif, return default (semua)
            if (activeFilters.length === 0 || activeFilters.length === 2) {
                return 'penjadwalan,perjanjian';
            }

            return activeFilters.join(',');
        }

        // Set default filter buttons (penjadwalan dan perjanjian aktif saat load)
        $('#filter-status-group .btn-filter-perjanjian').addClass('active');
        $('#filter-status-group .btn-filter-penjadwalan').addClass('active');

        // Inisialisasi Select2 untuk filter tanggal
        $('#filter-tanggal-dari, #filter-tanggal-sampai').select2({
            ajax: {
                url: "<?= base_url('operasi/PengkajianDafOpe/getTanggalOperasiDistinct') ?>",
                dataType: 'json',
                delay: 250,
                processResults: function(data) {
                    return { results: data };
                },
                cache: true
            }
        });

        // Fungsi untuk tambah kuota per kamar
        window.tambahKuotaKamar = function(kamarId, kamarIndex, tanggal) {
            let slotGrid = document.getElementById(`slot-grid-${kamarId}-${tanggal}`);
            if (!slotGrid) {
                console.error('Slot grid not found:', `slot-grid-${kamarId}-${tanggal}`);
                return;
            }

            let currentSlots = slotGrid.children.length;
            let newSlotIndex = currentSlots;

            // PERBAIKAN: Buat kuota baru dengan desain PERSIS seperti kuota kosong yang ada
            // Menggunakan struktur yang sama dengan slot kosong di renderListPerjanjianOperasi
            let newSlotHtml = `
                <div class="slot-operasi kosong"
                     data-tanggal="${tanggal}"
                     data-slot="${newSlotIndex + 1}"
                     data-kamar-id="${kamarId}"
                     data-kamar-nama="Kamar ${kamarId}"
                     onclick="pilihSlotKosong('${tanggal}', ${newSlotIndex + 1}, '${kamarId}', 'Kamar ${kamarId}', event)">
                    <div class="slot-info">
                        <div class="slot-kosong-icon">
                            <i class="fa fa-plus"></i>
                        </div>
                        <div class="slot-kosong-text">Kuota ${newSlotIndex + 1}</div>
                    </div>
                </div>
            `;

            slotGrid.insertAdjacentHTML('beforeend', newSlotHtml);

            // Update grid columns jika perlu (untuk tampilan yang lebih baik)
            let totalSlots = slotGrid.children.length;
            if (totalSlots > 4) {
                let columns = Math.ceil(Math.sqrt(totalSlots));
                slotGrid.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
            }

            toastr.success(`Kuota ${newSlotIndex + 1} berhasil ditambahkan untuk kamar ${kamarId}`, 'Berhasil');

            console.log('Added new slot:', newSlotIndex + 1, 'Total slots now:', totalSlots + 1);
        };

        <?php endif; ?>
    });
</script>
