<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianPraOperasiModel extends MY_Model
{
    protected $_table = 'medis.tb_pengkajian_operasi';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public function __construct()
    {
        parent::__construct();
    }
    public function rules()
    {
        return [
            [
                'field' => 'keluhan_utama',
                'label' => 'Keluhan utama',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'riwayat_penyakit_sekarang',
                'label' => 'Riwayat penyakit sekarang',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'riwayat_guna_obat',
                'label' => 'Riwayat penggunaan obat',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'alergi',
                'label' => 'Riwayat alergi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'psikologis',
                'label' => 'Psikologis',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'hubungan_anggota_keluarga',
                'label' => 'Hubungan dengan anggota keluarga',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'pencari_nafkah_utama',
                'label' => 'Pencari nafkah utama',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'tinggal_serumah_dengan',
                'label' => 'Tinggal serumah dengan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'kesadaran',
                'label' => 'Kesadaran',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'desk_pemeriksaan_fisik',
                'label' => 'Pemeriksaan fisik',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'desk_pemeriksaan_penunjang',
                'label' => 'Pemeriksaan penunjang/diagnostik',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'diagnosis_pra_operasi',
                'label' => 'Diagnosis pra operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'indikasi_operasi',
                'label' => 'Indikasi operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'rencana_tindakan_operasi',
                'label' => 'Rencana tindakan operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'perkiraan_lama_operasi',
                'label' => 'Perkiraan lama operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'persiapan_darah',
                'label' => 'Persiapan darah',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'persiapan_alatkhusus',
                'label' => 'Persiapan alat khusus',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesSimpan()
    {
        return [
            [
                'field' => 'nokun',
                'label' => 'Nomor kunjungan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'ruang_tujuan',
                'label' => 'Ruang tujuan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPenggunaanObat()
    {
        return [
            [
                'field' => 'desk_riwayat_guna_obat',
                'label' => 'Keterangan riwayat penggunaan obat',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesAlergi()
    {
        return [
            [
                'field' => 'isi_alergi[]',
                'label' => 'Sebutkan riwayat alergi',
                'rules' => 'trim|required|max_length[50]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 50 karakter',
                ]
            ],
            [
                'field' => 'reaksi_alergi',
                'label' => 'Reaksi alergi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPsikologis()
    {
        return [
            [
                'field' => 'desk_psikologis',
                'label' => 'Sebutkan psikologis lainnya',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPersiapanDarah()
    {
        return [
            [
                'field' => 'ket_persiapan_darah',
                'label' => 'Keterangan persiapan darah',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPersiapanAlatKhusus()
    {
        return [
            [
                'field' => 'ket_persiapan_alatkhusus',
                'label' => 'Keterangan persiapan alat khusus',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function simpan($data)
    {
        $this->db->insert($this->_table, $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_pengkajian_operasi.id', $id);
        $this->db->update($this->_table, $data);
    }

    public function ambil($nokun)
    {
        $this->db->select('diagnosis_pra_operasi, rencana_tindakan_operasi, perkiraan_lama_operasi');
        $this->db->from($this->_table);
        $this->db->where('nokun', $nokun);
        $this->db->order_by('id', 'desc');
        $query = $this->db->get();
        return $query->row_array();
    }

    /**
     * Method untuk menghitung jumlah operasi berdasarkan tanggal
     * Untuk ruang operasi ID=16 (Operasi Swasta Gedung C)
     */
    public function getJumlahOperasi($tanggal)
    {
        $this->db->select('COUNT(*) as jumlah');
        $this->db->from('remun_medis.perjanjian');
        $this->db->where('ID_RUANGAN', '105090104');
        $this->db->where('STATUS !=', '0');
        $this->db->where('TANGGAL', $tanggal);
        $query = $this->db->get();
        
        $result = $query->row_array();
        return $result['jumlah'] ?? 0;
    }

    /**
     * Method untuk mengecek apakah pasien sudah memiliki pendaftaran operasi pada tanggal tertentu
     * @param string $norm Nomor rekam medis pasien
     * @param string $tanggal Tanggal yang akan dicek
     * @return array|null Return data pasien jika sudah ada pendaftaran, null jika belum ada
     */
    public function cekPendaftaranOperasiByTanggal($norm, $tanggal)
    {
        $this->db->select('a.NOMR, a.TANGGAL, master.getNamaLengkap(a.NOMR) AS nama_pasien');
        $this->db->from('remun_medis.perjanjian a');
        $this->db->where('a.NOMR', $norm);
        $this->db->where('a.TANGGAL', $tanggal);
        $this->db->where('a.RENCANA', '11');
        $this->db->where('a.STATUS !=', '0');
        $query = $this->db->get();
        
        return $query->row_array();
    }

    /**
     * Method untuk mengambil detail operasi dengan server-side processing
     * Mendukung rentang tanggal dan kolom: NO, TANGGAL, WAKTU MULAI, WAKTU SELESAI, TINDAKAN, DOKTER, RUANGAN
     */
    public function getDetailOperasi($tanggal_awal = null, $tanggal_akhir = null, $tanggal = null, $start = 0, $length = 10, $search = '')
    {
        // Base query sesuai permintaan user
        $sql_base = "
            SELECT 
                a.tanggal_operasi,
                a.jam_operasi,
                CONCAT(
                    a.jam_operasi, 
                    ' - ', 
                    DATE_FORMAT(
                        ADDTIME(
                            STR_TO_DATE(a.jam_operasi, '%H:%i'),
                            SEC_TO_TIME(a.perkiraan_lama_operasi * 60)
                        ), 
                        '%H:%i'
                    )
                ) AS waktu,
                a.rencana_tindakan_operasi,
                db_rekammedis.getNamaLengkapDokter(a.dokter_bedah) AS nama_dokter,
                'Instalasi Bedah Gedung C' AS RUANGAN
            FROM medis.tb_pendaftaran_operasi a
            LEFT JOIN remun_medis.perjanjian b
                ON b.ID_PENDAFTARAN_OPERASI = a.id
                AND b.ID_RUANGAN = '105090104'
            WHERE
                b.ID_RUANGAN = '105090104'
                AND a.status != 0
                AND b.STATUS != 0
        ";
        
        $params = [];
        
        // Handle different date parameter scenarios
        if ($tanggal_awal && $tanggal_akhir) {
            // Range mode - untuk kalender 30 hari
            $sql_base .= " AND a.tanggal_operasi BETWEEN ? AND ?";
            $params[] = $tanggal_awal;
            $params[] = $tanggal_akhir;
        } elseif ($tanggal) {
            // Single date mode - untuk tanggal spesifik
            $sql_base .= " AND a.tanggal_operasi = ?";
            $params[] = $tanggal;
        } else {
            // Default: hari ini sampai 30 hari ke depan
            $sql_base .= " AND a.tanggal_operasi BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)";
        }
        
        // Add search condition if provided
        $search_condition = "";
        if (!empty($search)) {
            $search_condition = " AND (
                a.rencana_tindakan_operasi LIKE ? 
                OR db_rekammedis.getNamaLengkapDokter(a.dokter_bedah) LIKE ?
                OR a.jam_operasi LIKE ?
                OR DATE_FORMAT(a.tanggal_operasi, '%d/%m/%Y') LIKE ?
            )";
            $search_param = '%' . $search . '%';
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
        }

        // Get total records without pagination for filtered results
        $count_sql = "SELECT COUNT(*) as total FROM (" . $sql_base . $search_condition . ") as count_query";
        $count_query = $this->db->query($count_sql, $params);
        $total_filtered = $count_query->row_array()['total'];

        // Get total records without any filter
        $total_sql = "SELECT COUNT(*) as total FROM (" . $sql_base . ") as total_query";
        $total_query = $this->db->query($total_sql, array_slice($params, 0, count($params) - (empty($search) ? 0 : 4)));
        $total_records = $total_query->row_array()['total'];

        // Add ORDER BY and LIMIT for pagination
        $final_sql = $sql_base . $search_condition . " ORDER BY a.tanggal_operasi ASC, a.jam_operasi ASC LIMIT ?, ?";
        $params[] = (int)$start;
        $params[] = (int)$length;

        // Execute main query
        $query = $this->db->query($final_sql, $params);
        $result = $query->result_array();

        // Process data untuk format DataTables
        $data = [];
        $no = $start + 1;
        foreach ($result as $row) {
            $data[] = [
                'no' => $no++,
                'tanggal' => date('d/m/Y', strtotime($row['tanggal_operasi'])),
                'waktu_mulai' => $row['jam_operasi'] ?? '-',
                'waktu_selesai' => $row['waktu'] ? explode(' - ', $row['waktu'])[1] : '-',
                'tindakan' => $row['rencana_tindakan_operasi'] ?? '-',
                'dokter' => $row['nama_dokter'] ?? '-',
                'ruangan' => $row['RUANGAN']
            ];
        }

        return [
            'data' => $data,
            'recordsTotal' => $total_records,
            'recordsFiltered' => $total_filtered
        ];
    }

    /**
     * Method untuk mengambil data referensi berdasarkan jenis
     * Untuk select2 pada modal lengkapi reservasi
     */
    public function ambilReverensi($jenis, $id)
    {
        $q = $this->input->get('q');
        $this->db->select('JENIS, ID, DESKRIPSI');
        $this->db->from('master.referensi');
        $this->db->where('STATUS', 1);
        $this->db->where('JENIS', $jenis);

        // mulai pencarian
        if ($q) {
            $this->db->like('DESKRIPSI', $q);
        }
        // akhir pencarian

        if ($id) {
            $this->db->where_in('ID', $id);
        }

        $this->db->order_by('DESKRIPSI', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Method untuk mengambil data kamar operasi yang aktif
     */
    public function getKamarOperasi()
    {
        $sql = "SELECT id, nama FROM db_master.tb_kamar WHERE id_ruang = '105090104' AND status = 1 ORDER BY nama ASC";
        $query = $this->db->query($sql);
        return $query->result_array();
    }

    /**
     * Method untuk mengambil data perjanjian operasi untuk modal List Perjanjian Operasi
     * Menggunakan referensi dari getDetailOperasi dengan format khusus untuk slot operasi
     * Update: Distribusi dinamis berdasarkan kamar yang tersedia di database
     */
    public function getListPerjanjianOperasi($tanggal_mulai, $tanggal_akhir)
    {
        // Ambil data kamar yang aktif
        $kamar_list = $this->getKamarOperasi();
        $total_kamar = count($kamar_list);
        $slot_per_kamar = 4;
        $total_slot = $total_kamar * $slot_per_kamar;

        // Query untuk mengambil data operasi berdasarkan referensi getDetailOperasi
        $sql = "
            SELECT
                c.status AS status_jadwal,
                c.id AS id_penjadwalan,
                CASE WHEN c.id IS NULL THEN 'perjanjian' ELSE 'penjadwalan' END AS jenis,
                b.ID AS perjanjian,
                COALESCE(c.tgl_operasi, b.TANGGAL) AS tanggal_operasi,
                COALESCE(c.waktu_operasi, a.jam_operasi) AS jam_operasi,
                COALESCE(c.slot_operasi, a.slot_operasi) AS slot_operasi,
                COALESCE(c.kamar_operasi, a.kamar) AS kamar_operasi,
                CONCAT(b.NAMAPASIEN, ' [', b.NOMR, ']') AS pasien_info,
                db_rekammedis.getNamaLengkapDokter(a.dokter_bedah) AS nama_dokter,
                a.rencana_tindakan_operasi,
                CONCAT(
                    TIME_FORMAT(COALESCE(c.waktu_operasi, a.jam_operasi), '%H:%i'),
                    ' - ',
                    DATE_FORMAT(
                        ADDTIME(
                            STR_TO_DATE(COALESCE(c.waktu_operasi, a.jam_operasi), '%H:%i'),
                            SEC_TO_TIME(COALESCE(c.durasi_operasi, a.perkiraan_lama_operasi) * 60)
                        ),
                        '%H:%i'
                    )
                ) AS waktu_selesai,
                -- Hitung waktu selesai + 60 kanggo persiapan ruangan
                DATE_FORMAT(
                    ADDTIME(
                        STR_TO_DATE(COALESCE(c.waktu_operasi, a.jam_operasi), '%H:%i'),
                        SEC_TO_TIME((COALESCE(c.durasi_operasi, a.perkiraan_lama_operasi) + 60) * 60)
                    ),
                    '%H:%i'
                ) AS waktu_selesai_plus_interval,
                -- Tambahkan waktu mulai untuk sorting
                TIME_FORMAT(COALESCE(c.waktu_operasi, a.jam_operasi), '%H:%i') AS jam_mulai_sort,
                COALESCE(c.durasi_operasi, a.perkiraan_lama_operasi) AS durasi_final
            FROM medis.tb_pendaftaran_operasi a
            LEFT JOIN remun_medis.perjanjian b
                ON b.ID_PENDAFTARAN_OPERASI = a.id
                AND b.ID_RUANGAN = '105090104'
            LEFT JOIN perjanjian.penjadwalan_operasi c 
                ON c.id_perjanjian = b.ID
                AND c.status NOT IN (0,5)
            WHERE
                b.ID_RUANGAN = '105090104'
                AND a.status != 0
                AND b.STATUS != 0
                AND COALESCE(c.tgl_operasi, b.TANGGAL) BETWEEN ? AND ?
            ORDER BY COALESCE(c.tgl_operasi, b.TANGGAL) ASC, COALESCE(c.waktu_operasi, a.jam_operasi) ASC
        ";

        $query = $this->db->query($sql, [$tanggal_mulai, $tanggal_akhir]);
        $result = $query->result_array();

        // Organisir data per tanggal dengan slot dinamis berdasarkan jumlah kamar
        $data_per_tanggal = [];

        // PERBAIKAN: Inisialisasi 3 hari kerja dengan timezone Indonesia
        $timezone_indonesia = new DateTimeZone('Asia/Jakarta');
        $current_date = new DateTime($tanggal_mulai, $timezone_indonesia);
        $today = new DateTime('now', $timezone_indonesia);
        $hari_kerja_count = 0;

        // Debug log
        error_log("Model getListPerjanjianOperasi - tanggal_mulai: $tanggal_mulai");
        error_log("Model getListPerjanjianOperasi - today: " . $today->format('Y-m-d H:i:s'));
        error_log("Model getListPerjanjianOperasi - current_date: " . $current_date->format('Y-m-d H:i:s'));

        while ($hari_kerja_count < 3) {
            $tanggal = $current_date->format('Y-m-d');
            $day_of_week = (int)$current_date->format('N'); // 1=Senin, 7=Minggu

            // Cek apakah ini hari ini (timezone Indonesia)
            $is_today = $current_date->format('Y-m-d') === $today->format('Y-m-d');
            $is_today_sunday = $today->format('N') == 7;

            // Debug log
            error_log("Processing date: $tanggal, day_of_week: $day_of_week, is_today: " . ($is_today ? 'true' : 'false') . ", is_today_sunday: " . ($is_today_sunday ? 'true' : 'false'));

            // ATURAN BARU: Minggu SELALU di-skip (tidak ada pengecualian)
            // Hanya masukkan jika BUKAN Minggu (Senin-Sabtu saja)
            if ($day_of_week != 7) {
                $hari_nama = $this->getHariIndonesia($day_of_week);
                error_log("Added working day: $tanggal ($hari_nama)");

                $data_per_tanggal[$tanggal] = [
                    'tanggal' => $tanggal,
                    'hari' => $hari_nama,
                    'kamar_list' => $kamar_list,
                    'total_kamar' => $total_kamar,
                    'slot_per_kamar' => $slot_per_kamar,
                    'total_slot' => $total_slot,
                    'slots' => array_fill(0, $total_slot, null), // Slot kosong dinamis
                    'slots_by_kamar' => [] // Data operasi per kamar
                ];

                // Inisialisasi slots per kamar
                foreach ($kamar_list as $index => $kamar) {
                    $data_per_tanggal[$tanggal]['slots_by_kamar'][$index] = array_fill(0, $slot_per_kamar, null);
                }

                $hari_kerja_count++;
            } else {
                error_log("Skipped day: $tanggal (day_of_week: $day_of_week)");
            }

            $current_date->add(new DateInterval('P1D'));
        }

        // Hitung slot maksimal per kamar berdasarkan data yang ada
        $max_slots_per_kamar = [];
        foreach ($result as $row) {
            $kamar_operasi = $row['kamar_operasi'];
            $slot_operasi = (int)$row['slot_operasi'];

            if (!isset($max_slots_per_kamar[$kamar_operasi])) {
                $max_slots_per_kamar[$kamar_operasi] = 4; // Default minimum 4 slot
            }

            if ($slot_operasi > $max_slots_per_kamar[$kamar_operasi]) {
                $max_slots_per_kamar[$kamar_operasi] = $slot_operasi;
            }
        }

        // Update slot_per_kamar untuk setiap tanggal berdasarkan data maksimal
        foreach ($data_per_tanggal as $tanggal => &$hari_data) {
            foreach ($kamar_list as $index => $kamar) {
                $kamar_id = $kamar['id'];
                $max_slot = $max_slots_per_kamar[$kamar_id] ?? 4;

                // Update slots_by_kamar dengan slot dinamis
                $hari_data['slots_by_kamar'][$index] = array_fill(0, $max_slot, null);
                $hari_data['max_slots_per_kamar'] = $max_slots_per_kamar;
            }
        }

        // Organisir operasi berdasarkan slot_operasi dan kamar yang tersimpan
        foreach ($result as $row) {
            $tanggal = $row['tanggal_operasi'];
            $slot_operasi = $row['slot_operasi']; // 1-4 per kamar
            $kamar_operasi = $row['kamar_operasi'];
            
            if (isset($data_per_tanggal[$tanggal]) && $slot_operasi >= 1) {
                // Cari index kamar
                $kamar_index = -1;
                foreach ($kamar_list as $index => $kamar) {
                    if ($kamar['id'] == $kamar_operasi) {
                        $kamar_index = $index;
                        break;
                    }
                }

                if ($kamar_index >= 0) {
                    $max_slot_kamar = $max_slots_per_kamar[$kamar_operasi] ?? 4;

                    // Pastikan slot tidak melebihi maksimal untuk kamar ini
                    if ($slot_operasi <= $max_slot_kamar) {
                        $operasi_data = [
                            'id_penjadwalan' => $row['id_penjadwalan'],
                            'status_jadwal' => $row['status_jadwal'],
                            'jenis' => ($row['id_penjadwalan'] ? 'penjadwalan' : 'perjanjian'),
                            'pasien' => $row['pasien_info'],
                            'dokter' => $row['nama_dokter'],
                            'tindakan' => $row['rencana_tindakan_operasi'],
                            'waktu' => $row['waktu_selesai'],
                            'waktu_selesai_plus_interval' => $row['waktu_selesai_plus_interval'],
                            'jam_mulai' => $row['jam_mulai_sort'],
                            'jam_operasi' => $row['jam_operasi'],
                            'kamar_id' => $kamar_operasi,
                            'kamar_nama' => $kamar_list[$kamar_index]['nama'],
                            'durasi_final' => $row['durasi_final']
                        ];

                        // Isi slot berdasarkan kamar dan slot dengan logika prioritas
                        $slot_kamar_index = $slot_operasi - 1;

                        // Pastikan array slot_by_kamar ada untuk kamar ini
                        if (!isset($data_per_tanggal[$tanggal]['slots_by_kamar'][$kamar_index][$slot_kamar_index])) {
                            $data_per_tanggal[$tanggal]['slots_by_kamar'][$kamar_index][$slot_kamar_index] = null;
                        }

                        $existing_data = $data_per_tanggal[$tanggal]['slots_by_kamar'][$kamar_index][$slot_kamar_index];

                        // Prioritas: Jadwal (id_penjadwalan != null) > Perjanjian (id_penjadwalan == null)
                        if ($existing_data == null || ($existing_data['id_penjadwalan'] == null && $operasi_data['id_penjadwalan'] != null)) {
                            $data_per_tanggal[$tanggal]['slots_by_kamar'][$kamar_index][$slot_kamar_index] = $operasi_data;
                        }
                    }
                }
            }
        }

        return array_values($data_per_tanggal);
    }

    /**
     * Helper method untuk mendapatkan nama hari dalam bahasa Indonesia
     */
    private function getHariIndonesia($dayNumber)
    {
        $hari = [
            1 => 'Senin',
            2 => 'Selasa',
            3 => 'Rabu',
            4 => 'Kamis',
            5 => 'Jumat',
            6 => 'Sabtu',
            7 => 'Minggu'
        ];

        return $hari[$dayNumber] ?? 'Tidak Diketahui';
    }

    /**
     * Method untuk mengambil data perjanjian operasi dengan server-side processing
     * Berdasarkan referensi query dari refrensi query.md
     */
    public function getDaftarPerjanjianOperasi($tanggal_dari = null, $tanggal_sampai = null, $status_filter = 'penjadwalan,perjanjian', $me_filter = false, $id_dokter_session = 0, $start = 0, $length = 10, $search = '', $draw = 1)
{
    // Fungsi helper untuk membangun kondisi WHERE yang sama
    $buildWhereConditions = function() use ($tanggal_dari, $tanggal_sampai, $status_filter, $me_filter, $id_dokter_session, $search) {
        // Kondisi dasar yang wajib ada
        $this->db->where('rmp.STATUS !=', 0);
        $this->db->where('rmp.ID_RUANGAN', '105090104');
        $this->db->where('wlo.id IS NOT NULL', null, false);
        $this->db->where('(ppo.status NOT IN (0, 5) OR ppo.id IS NULL)', null, false);

        // Filter tanggal masa lalu - HANYA jika tidak ada filter tanggal khusus
        if (empty($tanggal_dari) && empty($tanggal_sampai)) {
            // Tampilkan data mulai dari kemarin untuk menghindari masalah timezone
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $this->db->where('IFNULL(ppo.tgl_operasi, rmp.TANGGAL) >=', $yesterday);
        }

        // Filter tanggal khusus (jika dipilih, override filter default)
        if (!empty($tanggal_dari) && !empty($tanggal_sampai)) {
            $this->db->where('IFNULL(ppo.tgl_operasi, rmp.TANGGAL) >=', $tanggal_dari);
            $this->db->where('IFNULL(ppo.tgl_operasi, rmp.TANGGAL) <=', $tanggal_sampai);
        }

        // Filter Me (hanya jika aktif)
        if ($me_filter === true || $me_filter === 'true') {
            if ($id_dokter_session > 0) {
                $this->db->where('wlo.id_dokter', $id_dokter_session);
            }
        }

        // Search filter
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('master.getNamaLengkap(wlo.norm)', $search);
            $this->db->or_like('wlo.norm', $search);
            $this->db->or_like('db_rekammedis.getNamaLengkapDokter(wlo.id_dokter)', $search);
            $this->db->or_like('DATE_FORMAT(IFNULL(ppo.tgl_operasi, rmp.TANGGAL), "%d/%m/%Y")', $search);
            $this->db->or_like('TIME_FORMAT(COALESCE(ppo.waktu_operasi, tpo.jam_operasi), "%H:%i")', $search);
            $this->db->group_end();
        }

        // Status filter - Logic diperbaiki
        if (!empty($status_filter) && $status_filter !== 'penjadwalan,perjanjian') {
            $statusArray = explode(',', $status_filter);
            $statusArray = array_map('trim', $statusArray); // Bersihkan whitespace
            
            // Jika hanya satu status yang dipilih
            if (count($statusArray) == 1) {
                if (in_array('penjadwalan', $statusArray)) {
                    // Hanya tampilkan yang sudah dijadwalkan (ada di tabel penjadwalan_operasi)
                    $this->db->where('ppo.id IS NOT NULL');
                } elseif (in_array('perjanjian', $statusArray)) {
                    // Hanya tampilkan yang belum dijadwalkan (tidak ada di tabel penjadwalan_operasi)
                    $this->db->where('ppo.id IS NULL');
                }
            }
            // Jika kedua status dipilih atau tidak ada yang dipilih, tampilkan semua
        }
    };

    // Fungsi helper untuk membangun JOIN yang sama
    $buildJoins = function() {
        $this->db->from('medis.tb_pendaftaran_operasi tpo');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('db_master.tb_kamar dk', "dk.id = IFNULL(ppo.kamar_operasi, tpo.kamar) AND dk.id_ruang = '105090104'", 'left');
        $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID', 'left');
    };

    // === COUNT TOTAL RECORDS ===
    $this->db->select('COUNT(*) as total', false);
    $buildJoins();
    $buildWhereConditions();
    
    $countQuery = $this->db->get();
    $recordsTotal = $countQuery->row()->total;
    $recordsFiltered = $recordsTotal;

    // Reset query builder untuk query data
    $this->db->reset_query();

    // === GET DATA ===
    $this->db->select("
        rmp.ID,
        ppo.id AS id_penjadwalan,
        rr.id AS id_reservasi,
        tpo.id AS id_pendaftaran,
        IFNULL(ppo.tgl_operasi, rmp.TANGGAL) AS tgl_operasi,
        dk.nama AS kamar_operasi,
        CONCAT(wlo.norm, ' - ', master.getNamaLengkap(wlo.norm)) AS nama_pasien,
        COALESCE(ppo.waktu_operasi, tpo.jam_operasi) AS waktu_operasi,
        COALESCE(ppo.slot_operasi, tpo.slot_operasi) AS slot_operasi,
        CONCAT(
            TIME_FORMAT(COALESCE(ppo.waktu_operasi, tpo.jam_operasi), '%H:%i'),
            ' - ',
            TIME_FORMAT(
                ADDTIME(
                    STR_TO_DATE(COALESCE(ppo.waktu_operasi, tpo.jam_operasi), '%H:%i'),
                    SEC_TO_TIME(COALESCE(ppo.durasi_operasi, tpo.perkiraan_lama_operasi) * 60)
                ),
                '%H:%i'
            )
        ) AS waktu_selesai,
        db_rekammedis.getNamaLengkapDokter(wlo.id_dokter) AS nama_dokter,
        CASE WHEN ppo.id IS NOT NULL THEN 'Penjadwalan' ELSE 'Perjanjian' END AS status,
        CASE
            WHEN ppo.id IS NULL
                AND IFNULL(ppo.slot_operasi, tpo.slot_operasi) IS NOT NULL
                AND IFNULL(ppo.kamar_operasi, tpo.kamar) IS NOT NULL
            THEN (
                SELECT COUNT(*)
                FROM perjanjian.penjadwalan_operasi ppo2
                WHERE ppo2.kamar_operasi = IFNULL(ppo.kamar_operasi, tpo.kamar)
                AND ppo2.slot_operasi = IFNULL(ppo.slot_operasi, tpo.slot_operasi)
                AND ppo2.tgl_operasi = IFNULL(ppo.tgl_operasi, rmp.TANGGAL)
                AND ppo2.status = 1
            )
            ELSE 0
        END AS konflik_slot
    ", false);

    $buildJoins();
    $buildWhereConditions();

    // Custom ordering: penjadwalan dulu, kemudian perjanjian, urutkan berdasarkan tanggal dan jam
    $this->db->order_by("
        CASE
            WHEN ppo.id IS NOT NULL THEN 1
            WHEN ppo.id IS NULL THEN 2
        END
    ", null, false);

    $this->db->order_by("IFNULL(ppo.tgl_operasi, rmp.TANGGAL)", "ASC", false);
    $this->db->order_by("COALESCE(ppo.waktu_operasi, tpo.jam_operasi)", "ASC", false);

    // Pagination
    $this->db->limit($length, $start);

    $query = $this->db->get();
    $result = $query->result_array();

    // Format data untuk DataTables
    $data = [];
    $no = $start + 1;
    foreach ($result as $row) {
        // Deteksi konflik untuk styling
        $konflik_class = '';
        $konflik_note = '';
        if ($row['konflik_slot'] > 1) {
            $konflik_class = 'table-danger';
            $konflik_note = '<br><small class="text-danger">⚠️ Konflik: Slot kamar ' . $row['kamar_operasi'] . ' terisi pasien lain</small>';
        }

        // Format tanggal d/m/Y
        $tanggal_format = date('d/m/Y', strtotime($row['tgl_operasi']));

        $data[] = [
            'no' => $no++,
            'tanggal_operasi' => $tanggal_format . $konflik_note,
            'nama_pasien' => $row['nama_pasien'] ?? '-',
            'nama_dokter' => $row['nama_dokter'] ?? '-',
            'jam_operasi' => $row['waktu_selesai'] ?? '-', 
            'status' => $row['status'] ?? '-',
            'DT_RowClass' => $konflik_class
        ];
    }

    // Return format sesuai standar DataTables
    return [
        'draw' => intval($draw),
        'recordsTotal' => intval($recordsTotal),
        'recordsFiltered' => intval($recordsFiltered),
        'data' => $data
    ];
}

    /**
     * Method untuk mengambil distinct tanggal operasi untuk filter
     */
    public function getTanggalOperasiDistinct()
    {
        $this->db->select('DISTINCT IFNULL(ppo.tgl_operasi, rmp.TANGGAL) AS tgl_operasi');
        $this->db->from('medis.tb_pendaftaran_operasi tpo');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');

        $this->db->where('rmp.STATUS !=', 0);
        $this->db->where('rmp.ID_RUANGAN', '105090104');
        $this->db->where('wlo.id IS NOT NULL', null, false);
        $this->db->where('(ppo.status NOT IN (0, 5) OR ppo.id IS NULL)', null, false);

        $this->db->order_by('tgl_operasi', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }
}

/* End of file PengkajianPraOperasiModel.php */
/* Location: ./application/models/operasi/PengkajianPraOperasiModel.php */
